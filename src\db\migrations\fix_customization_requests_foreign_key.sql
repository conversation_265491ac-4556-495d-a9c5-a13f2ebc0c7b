-- Fix the foreign key constraint on customization_requests table
-- to add ON DELETE CASCADE for product_id

-- First, drop the existing constraint
ALTER TABLE customization_requests DROP CONSTRAINT IF EXISTS customization_requests_product_id_fkey;

-- Then, add it back with ON DELETE CASCADE
ALTER TABLE customization_requests 
  ADD CONSTRAINT customization_requests_product_id_fkey 
  FOREIGN KEY (product_id) 
  REFERENCES products(id) 
  ON DELETE CASCADE;

-- Add a comment to explain the purpose of this migration
COMMENT ON CONSTRAINT customization_requests_product_id_fkey ON customization_requests IS 
  'Foreign key to products table with cascade delete - when a product is deleted, all related customization requests are also deleted';
