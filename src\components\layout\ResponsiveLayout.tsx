
import React from "react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import MobileBottomNav from "@/components/layout/MobileBottomNav";
import FloatingWhatsApp from "@/components/ui/floating-whatsapp";
import { useAuth } from "@/context/SupabaseAuthContext";
import { cn } from "@/lib/utils";

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  hideFooter?: boolean;
  className?: string;
  fullWidth?: boolean;
}

/**
 * A responsive layout component with Navbar, Footer, and Mobile Navigation
 */
const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  children,
  hideFooter = false,
  className,
  fullWidth = false,
}) => {
  const { isAdmin } = useAuth();
  const showMobileNav = !isAdmin();

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <main
        className={cn(
          "flex-1 pt-20 md:pt-28 pb-16",
          showMobileNav && "pb-24 md:pb-16", // Extra padding for mobile nav
          className
        )}
      >
        {children}
      </main>
      {!hideFooter && <Footer />}
      {showMobileNav && <MobileBottomNav />}

      {/* Floating WhatsApp Button - Show on all pages except admin */}
      {!isAdmin() && <FloatingWhatsApp />}
    </div>
  );
};

export default ResponsiveLayout;
