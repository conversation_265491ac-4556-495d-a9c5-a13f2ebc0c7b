/**
 * Product Service
 *
 * This module provides functions to interact with product data in Supabase.
 */
import { supabase } from '@/lib/supabase';
import { handleSupabaseError, showSuccessToast, testSupabaseConnection } from '@/utils/supabaseHelpers';
import { FrontendProduct, ProductInput } from './types';
import type { Category } from '@/types/category';
import { mapSupabaseProductToFrontend } from './mappers';

/**
 * Get all products
 * @returns Array of frontend products
 */
export const getProducts = async (): Promise<FrontendProduct[]> => {
  try {
    // Test Supabase connection
    await testSupabaseConnection();

    // Proceed with the actual query
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        category:categories(id, name),
        images:product_images(*),
        rating_summary:product_ratings_summary!product_id(*)
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Supabase query error:', error);
      // Log more details about the error
      console.error('Error details:', {
        message: error.message,
        code: error.code,
        hint: error.hint,
        details: error.details
      });
      throw error;
    }

    if (!data || data.length === 0) {
      console.warn('No products found in database - this indicates the products table is empty');
      console.warn('Please run the database setup script to populate products');
      return [];
    }

    // Map the products to frontend format
    return data.map(mapSupabaseProductToFrontend);
  } catch (error) {
    handleSupabaseError(error, 'getProducts');
    // Return empty array to allow the app to continue
    return [];
  }
};

/**
 * Get a single product by ID
 * @param id Product ID
 * @returns Frontend product or null if not found
 */
export const getProductById = async (id: string): Promise<FrontendProduct | null> => {
  try {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        category:categories(id, name),
        images:product_images(*),
        rating_summary:product_ratings_summary!product_id(*)
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Supabase query error:', error);
      throw error;
    }

    if (!data) {
      console.warn(`Product with ID ${id} not found`);
      return null;
    }

    return mapSupabaseProductToFrontend(data);
  } catch (error) {
    handleSupabaseError(error, `getProductById(${id})`);
    // Return null to allow the app to handle the missing product
    return null;
  }
};

/**
 * Get all categories
 * @returns Array of categories
 */
export const getCategories = async (): Promise<Category[]> => {
  try {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .order('name');

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    handleSupabaseError(error, 'getCategories');
    return [];
  }
};

/**
 * Update product stock
 * @param id Product ID
 * @param stock New stock quantity
 * @returns Success status
 */
export const updateProductStock = async (id: string, stock: number): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('products')
      .update({ stock, updated_at: new Date().toISOString() })
      .eq('id', id);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    handleSupabaseError(error, 'updateProductStock');
    return false;
  }
};
