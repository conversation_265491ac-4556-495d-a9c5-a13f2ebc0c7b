import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';

export interface Customer {
  id: string;
  name: string;
  email: string;
  phone?: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at?: string;
  orders_count?: number;
}

/**
 * Fetches all customers from Supabase
 * @returns Array of customers
 */
export const getCustomers = async (): Promise<Customer[]> => {
  try {
    console.log('Fetching customers from Supabase...');

    // Get all user profiles
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('role', 'user') // Only get regular users, not admins
      .order('created_at', { ascending: false });

    console.log('Supabase response:', { data, error });

    if (error) {
      console.error('Error fetching customers:', error);
      toast({
        title: 'Error fetching customers',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      return [];
    }

    // Transform the data to match our Customer interface
    const customers: Customer[] = data.map((profile: any) => ({
      id: profile.id,
      name: profile.display_name || 'Unknown',
      email: profile.email || '',
      phone: profile.phone || '',
      status: profile.status || 'active',
      created_at: profile.created_at,
      updated_at: profile.updated_at,
      orders_count: 0 // Default value, will be updated below
    }));

    // Get order counts for each customer
    // This is a separate query to avoid complex joins
    try {
      console.log('Fetching order counts from Supabase...');

      const { data: orderData, error: orderError } = await supabase
        .from('orders')
        .select('user_id, count(*)', { count: 'exact' })
        .group('user_id');

      console.log('Order counts response:', { orderData, orderError });

      if (!orderError && orderData) {
        // Create a map of user_id to order count
        const orderCountMap = orderData.reduce((acc: Record<string, number>, item: any) => {
          acc[item.user_id] = parseInt(item.count, 10);
          return acc;
        }, {});

        console.log('Order count map:', orderCountMap);

        // Update the customers with their order counts
        customers.forEach(customer => {
          customer.orders_count = orderCountMap[customer.id] || 0;
        });

        console.log('Customers with order counts:', customers);
      }
    } catch (orderError) {
      console.error('Error fetching order counts:', orderError);
      // Continue with default order counts
    }

    return customers;
  } catch (error) {
    console.error('Error in getCustomers:', error);
    return [];
  }
};

/**
 * Fetches a single customer by ID
 * @param customerId The customer ID
 * @returns The customer or null if not found
 */
export const getCustomerById = async (customerId: string): Promise<Customer | null> => {
  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', customerId)
      .single();

    if (error) {
      console.error('Error fetching customer:', error);
      return null;
    }

    // Transform to Customer interface
    const customer: Customer = {
      id: data.id,
      name: data.display_name || 'Unknown',
      email: data.email || '',
      phone: data.phone || '',
      status: data.status || 'active',
      created_at: data.created_at,
      updated_at: data.updated_at,
      orders_count: 0 // Default value
    };

    // Get order count for this customer
    try {
      const { count, error: orderError } = await supabase
        .from('orders')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', customerId);

      if (!orderError) {
        customer.orders_count = count || 0;
      }
    } catch (orderError) {
      console.error('Error fetching order count:', orderError);
      // Continue with default order count
    }

    return customer;
  } catch (error) {
    console.error('Error in getCustomerById:', error);
    return null;
  }
};

/**
 * Updates a customer's status in Supabase
 * @param customerId The customer ID
 * @param status The new status ('active' or 'inactive')
 * @returns True if successful, false otherwise
 */
export const updateCustomerStatus = async (
  customerId: string,
  status: 'active' | 'inactive'
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('user_profiles')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', customerId);

    if (error) {
      console.error('Error updating customer status:', error);
      toast({
        title: 'Error updating customer',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in updateCustomerStatus:', error);
    return false;
  }
};

/**
 * Gets a customer's order history
 * @param customerId The customer ID
 * @returns Array of orders
 */
export const getCustomerOrders = async (customerId: string): Promise<any[]> => {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        order_items:order_items(
          *,
          product:products(name, price)
        )
      `)
      .eq('user_id', customerId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching customer orders:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getCustomerOrders:', error);
    return [];
  }
};
