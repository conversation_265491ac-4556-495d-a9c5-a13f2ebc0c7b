-- Simple Payment System Fix
-- Run this step by step in your Supabase query editor

-- STEP 1: First, let's see what payment methods we currently have
SELECT payment_method, COUNT(*) as count
FROM orders
GROUP BY payment_method
ORDER BY count DESC;

-- STEP 2: Fix existing payment method data (update ALL variations)
UPDATE orders SET payment_method = 'cash_on_delivery'
WHERE payment_method ILIKE '%cash%'
   OR payment_method ILIKE '%cod%'
   OR payment_method ILIKE '%delivery%'
   OR payment_method IS NULL
   OR payment_method = '';

UPDATE orders SET payment_method = 'online_payment'
WHERE payment_method ILIKE '%online%'
   OR payment_method ILIKE '%razorpay%'
   OR payment_method ILIKE '%upi%'
   OR payment_method ILIKE '%card%'
   OR payment_method ILIKE '%net%'
   OR payment_method ILIKE '%wallet%';

-- STEP 3: Set any remaining unknown payment methods to cash_on_delivery
UPDATE orders SET payment_method = 'cash_on_delivery'
WHERE payment_method NOT IN ('cash_on_delivery', 'online_payment', 'razorpay', 'upi', 'card', 'netbanking', 'wallet');

-- STEP 4: Remove any existing problematic constraint
ALTER TABLE orders DROP CONSTRAINT IF EXISTS orders_payment_method_check;

-- STEP 5: Add the correct constraint
ALTER TABLE orders ADD CONSTRAINT orders_payment_method_check
CHECK (payment_method IN ('cash_on_delivery', 'online_payment', 'razorpay', 'upi', 'card', 'netbanking', 'wallet'));

-- STEP 4: Add payment_status column if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'orders' AND column_name = 'payment_status'
  ) THEN
    ALTER TABLE orders ADD COLUMN payment_status TEXT
    CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded'))
    DEFAULT 'pending';
  END IF;
END $$;

-- STEP 5: Add payment_reference column if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'orders' AND column_name = 'payment_reference'
  ) THEN
    ALTER TABLE orders ADD COLUMN payment_reference TEXT;
  END IF;
END $$;

-- STEP 6: Set default payment_status for existing orders
UPDATE orders SET payment_status = 'pending' WHERE payment_status IS NULL;

-- STEP 7: Verify the fix worked
SELECT
  payment_method,
  payment_status,
  COUNT(*) as count
FROM orders
GROUP BY payment_method, payment_status
ORDER BY payment_method, payment_status;
