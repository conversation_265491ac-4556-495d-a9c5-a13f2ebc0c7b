/**
 * Revenue Analytics Service
 *
 * This module provides functions to fetch revenue-related analytics data.
 */
import { supabase } from '@/lib/supabase';
import { handleSupabaseError } from '@/utils/supabaseHelpers';
import { OrderStats, SalesTrend, WeeklyPerformance } from './types';

/**
 * Get total revenue from completed orders
 * @returns Total revenue
 */
export const getTotalRevenue = async (): Promise<number> => {
  try {
    const { data, error } = await supabase.rpc('get_total_revenue');

    if (error) {
      console.error('Error fetching total revenue:', error);
      return 0;
    }

    return data || 0;
  } catch (error) {
    handleSupabaseError(error, 'getTotalRevenue', false);
    return 0;
  }
};

/**
 * Get order statistics (total, completed, processing, pending)
 * @returns Order statistics
 */
export const getOrderStats = async (): Promise<OrderStats> => {
  try {
    // Get total orders
    const { count: total, error: totalError } = await supabase
      .from('orders')
      .select('*', { count: 'exact', head: true });

    if (totalError) {
      throw totalError;
    }

    // Get completed orders
    const { count: completed, error: completedError } = await supabase
      .from('orders')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'delivered');

    if (completedError) {
      throw completedError;
    }

    // Get processing orders
    const { count: processing, error: processingError } = await supabase
      .from('orders')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'processing');

    if (processingError) {
      throw processingError;
    }

    // Get pending orders
    const { count: pending, error: pendingError } = await supabase
      .from('orders')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'pending');

    if (pendingError) {
      throw pendingError;
    }

    return {
      total: total || 0,
      completed: completed || 0,
      processing: processing || 0,
      pending: pending || 0
    };
  } catch (error) {
    handleSupabaseError(error, 'getOrderStats', false);
    return {
      total: 0,
      completed: 0,
      processing: 0,
      pending: 0
    };
  }
};

/**
 * Get sales trend (monthly sales for the past 6 months)
 * @returns Sales trend data
 */
export const getSalesTrend = async (): Promise<SalesTrend[]> => {
  try {
    const { data, error } = await supabase.rpc('get_monthly_sales');

    if (error) {
      console.error('Error fetching sales trend:', error);
      return [];
    }

    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const currentMonth = new Date().getMonth();

    // Create a map of month number to sales amount
    const salesMap = new Map();
    data.forEach((item: any) => {
      // Handle bigint type from Supabase by converting to string first
      salesMap.set(parseInt(String(item.month)), parseFloat(String(item.total_sales)));
    });

    // Generate the past 6 months of data
    const result = [];
    for (let i = 5; i >= 0; i--) {
      const monthIndex = (currentMonth - i + 12) % 12;
      const monthName = months[monthIndex];
      const sales = salesMap.get(monthIndex + 1) || 0; // Month in DB is 1-indexed

      result.push({
        name: monthName,
        sales: sales
      });
    }

    // Add forecast for the next 3 months
    // This is a simple forecast based on the average of the last 3 months
    const lastThreeMonths = result.slice(-3);
    const avgSales = lastThreeMonths.reduce((sum, item) => sum + item.sales, 0) / lastThreeMonths.length;

    for (let i = 1; i <= 3; i++) {
      const monthIndex = (currentMonth + i) % 12;
      const monthName = months[monthIndex];

      result.push({
        name: monthName,
        forecast: avgSales * (1 + (Math.random() * 0.2 - 0.1)) // Add some randomness to the forecast
      });
    }

    return result;
  } catch (error) {
    handleSupabaseError(error, 'getSalesTrend', false);
    return [];
  }
};

/**
 * Get weekly performance (daily orders and revenue for the past week)
 * @returns Weekly performance data
 */
export const getWeeklyPerformance = async (): Promise<WeeklyPerformance[]> => {
  try {
    const { data, error } = await supabase.rpc('get_daily_sales');

    if (error) {
      console.error('Error fetching weekly performance:', error);
      return [];
    }

    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    const today = new Date().getDay(); // 0 = Sunday, 6 = Saturday

    // Create maps of day of week to orders and revenue
    const ordersMap = new Map();
    const revenueMap = new Map();

    data.forEach((item: any) => {
      // Convert PostgreSQL day of week (1-7, where 1 is Monday) to JS day of week (0-6, where 0 is Sunday)
      // Handle bigint type from Supabase by converting to string first
      const dayOfWeek = (parseInt(String(item.day_of_week)) - 1 + 7) % 7;
      ordersMap.set(dayOfWeek, parseInt(String(item.order_count)));
      revenueMap.set(dayOfWeek, parseFloat(String(item.total_sales)));
    });

    // Generate the past week of data
    const result = [];
    for (let i = 6; i >= 0; i--) {
      const dayIndex = (today - i + 7) % 7;
      const dayName = days[dayIndex === 0 ? 6 : dayIndex - 1]; // Adjust for Sunday

      result.push({
        name: dayName,
        orders: ordersMap.get(dayIndex) || 0,
        revenue: revenueMap.get(dayIndex) || 0
      });
    }

    return result;
  } catch (error) {
    handleSupabaseError(error, 'getWeeklyPerformance', false);
    return [];
  }
};
