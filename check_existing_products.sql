-- ============================================================================
-- CHECK EXISTING PRODUCTS IN DATABASE
-- This script will show what products are already in your database
-- ============================================================================

SELECT 'CHECKING EXISTING PRODUCTS IN DATABASE' as title;
SELECT '=======================================' as separator;

-- ============================================================================
-- STEP 1: CHECK PRODUCTS TABLE STRUCTURE
-- ============================================================================

SELECT 'STEP 1: Products table structure' as step;

SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'products' 
ORDER BY ordinal_position;

-- ============================================================================
-- STEP 2: COUNT EXISTING DATA
-- ============================================================================

SELECT 'STEP 2: Data counts' as step;

SELECT 
  'products' as table_name,
  COUNT(*) as row_count
FROM products
UNION ALL
SELECT 
  'categories' as table_name,
  COUNT(*) as row_count
FROM categories
UNION ALL
SELECT 
  'product_images' as table_name,
  COUNT(*) as row_count
FROM product_images;

-- ============================================================================
-- STEP 3: SHOW ALL EXISTING PRODUCTS
-- ============================================================================

SELECT 'STEP 3: All existing products' as step;

SELECT 
  id,
  name,
  description,
  price,
  sale_price,
  is_sale,
  is_new,
  is_featured,
  category_id,
  status,
  stock,
  sku,
  pg_typeof(id) as id_type,
  created_at
FROM products 
ORDER BY created_at DESC;

-- ============================================================================
-- STEP 4: SHOW ALL EXISTING CATEGORIES
-- ============================================================================

SELECT 'STEP 4: All existing categories' as step;

SELECT 
  id,
  name,
  description,
  image_url,
  is_active,
  pg_typeof(id) as id_type,
  created_at
FROM categories 
ORDER BY name;

-- ============================================================================
-- STEP 5: SHOW PRODUCT IMAGES
-- ============================================================================

SELECT 'STEP 5: Product images' as step;

SELECT 
  pi.id,
  pi.product_id,
  p.name as product_name,
  pi.image_url,
  pi.alt_text,
  pi.is_primary,
  pi.sort_order
FROM product_images pi
LEFT JOIN products p ON pi.product_id = p.id
ORDER BY p.name, pi.sort_order;

-- ============================================================================
-- STEP 6: CHECK FOR ORPHANED DATA
-- ============================================================================

SELECT 'STEP 6: Data integrity checks' as step;

-- Products without categories
SELECT 'Products without valid categories:' as check_type;
SELECT 
  p.id,
  p.name,
  p.category_id,
  CASE 
    WHEN c.id IS NULL THEN 'MISSING CATEGORY'
    ELSE 'OK'
  END as category_status
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
WHERE c.id IS NULL;

-- Products without images
SELECT 'Products without images:' as check_type;
SELECT 
  p.id,
  p.name,
  CASE 
    WHEN pi.product_id IS NULL THEN 'NO IMAGES'
    ELSE 'HAS IMAGES'
  END as image_status
FROM products p
LEFT JOIN product_images pi ON p.id = pi.product_id
WHERE pi.product_id IS NULL;

-- ============================================================================
-- STEP 7: CHECK PRODUCT STATUS AND VISIBILITY
-- ============================================================================

SELECT 'STEP 7: Product visibility analysis' as step;

SELECT 
  status,
  COUNT(*) as product_count,
  CASE 
    WHEN status = 'active' THEN 'SHOULD BE VISIBLE'
    WHEN status = 'draft' THEN 'HIDDEN - DRAFT'
    WHEN status = 'deleted' THEN 'HIDDEN - DELETED'
    ELSE 'UNKNOWN STATUS'
  END as visibility
FROM products 
GROUP BY status;

-- Show products that should be visible
SELECT 'Active products that should be visible:' as info;
SELECT 
  id,
  name,
  price,
  stock,
  CASE 
    WHEN stock > 0 THEN 'IN STOCK'
    ELSE 'OUT OF STOCK'
  END as stock_status
FROM products 
WHERE status = 'active'
ORDER BY name;

-- ============================================================================
-- STEP 8: SAMPLE PRODUCT QUERY (LIKE FRONTEND DOES)
-- ============================================================================

SELECT 'STEP 8: Testing frontend-style query' as step;

-- This mimics what the frontend productService.ts does
SELECT 
  p.*,
  c.id as category_id_joined,
  c.name as category_name,
  pi.image_url as primary_image
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = true
WHERE p.status = 'active'
ORDER BY p.created_at DESC
LIMIT 5;

-- ============================================================================
-- STEP 9: SUMMARY AND RECOMMENDATIONS
-- ============================================================================

SELECT 'STEP 9: Summary and recommendations' as step;

DO $$
DECLARE
  total_products integer;
  active_products integer;
  products_with_images integer;
  products_with_categories integer;
BEGIN
  SELECT COUNT(*) FROM products INTO total_products;
  SELECT COUNT(*) FROM products WHERE status = 'active' INTO active_products;
  SELECT COUNT(DISTINCT pi.product_id) FROM product_images pi INTO products_with_images;
  SELECT COUNT(*) FROM products p JOIN categories c ON p.category_id = c.id INTO products_with_categories;
  
  RAISE NOTICE '=== SUMMARY ===';
  RAISE NOTICE 'Total products in database: %', total_products;
  RAISE NOTICE 'Active products (should be visible): %', active_products;
  RAISE NOTICE 'Products with images: %', products_with_images;
  RAISE NOTICE 'Products with valid categories: %', products_with_categories;
  RAISE NOTICE '';
  
  IF total_products = 0 THEN
    RAISE NOTICE '🔥 ISSUE: No products in database at all';
    RAISE NOTICE '📋 ACTION: Need to add products to database';
  ELSIF active_products = 0 THEN
    RAISE NOTICE '🔥 ISSUE: No active products (all are draft/deleted)';
    RAISE NOTICE '📋 ACTION: Update product status to active';
  ELSIF products_with_images = 0 THEN
    RAISE NOTICE '🔥 ISSUE: Products exist but no images';
    RAISE NOTICE '📋 ACTION: Add product images';
  ELSIF products_with_categories = 0 THEN
    RAISE NOTICE '🔥 ISSUE: Products exist but no valid categories';
    RAISE NOTICE '📋 ACTION: Fix category relationships';
  ELSE
    RAISE NOTICE '✅ Products exist and look properly configured';
    RAISE NOTICE '📋 ACTION: Check frontend code for other issues';
  END IF;
END $$;

SELECT 'ANALYSIS COMPLETE - Check the output above for your current database state' as result;
