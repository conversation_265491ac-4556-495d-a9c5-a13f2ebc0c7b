# Supabase Edge Functions

This directory contains Edge Functions for the Badhees e-commerce application.

## Email Notification System

The unified email notification system uses a single Supabase Edge Function to send all types of transactional emails:

1. **Welcome Email** - Sent when a new user registers
2. **Order Confirmation** - Sent immediately after a successful payment
3. **Delivery Confirmation** - Sent when an admin changes the order status to "delivered"

### Setup Instructions

#### 1. Create the Email Logs Table

The `email_logs` table is created using the migration script in `supabase/migrations/20240101000000_create_email_logs_table.sql`. This table tracks sent emails to prevent duplicates.

#### 2. Set Environment Variables

Set the following environment variables in your Supabase project:

```bash
npx supabase secrets set EMAIL_HOST=smtp.gmail.com
npx supabase secrets set EMAIL_PORT=587
npx supabase secrets set EMAIL_USERNAME=<EMAIL>
npx supabase secrets set EMAIL_PASSWORD=your-app-password
npx supabase secrets set EMAIL_FROM=<EMAIL>
npx supabase secrets set SITE_URL=https://thebadhees.com
```

For Gmail, you'll need to create an App Password in your Google Account settings.

#### 3. Deploy the Edge Function

```bash
cd final1
npx supabase functions deploy email-service --use-docker=false
```

Note: The `--use-docker=false` flag allows you to deploy without having Docker installed.

### Local Development

To test the email system locally:

1. Start the Supabase local development server:

```bash
npx supabase start
```

2. Deploy the function to the local environment:

```bash
npx supabase functions serve email-service --no-verify-jwt
```

3. Test the function using curl or Postman:

```bash
# Test welcome email
curl -X POST http://localhost:54321/functions/v1/email-service \
  -H "Content-Type: application/json" \
  -d '{"type":"welcome_email","userId":"your-user-id"}'

# Test order confirmation email
curl -X POST http://localhost:54321/functions/v1/email-service \
  -H "Content-Type: application/json" \
  -d '{"type":"order_confirmation","orderId":"your-order-id"}'
```

### Troubleshooting

#### Common Issues

1. **Emails not sending**
   - Check the Supabase Edge Function logs: `npx supabase functions logs email-service`
   - Verify SMTP credentials are correct
   - Ensure the order or user exists in the database

2. **Duplicate emails**
   - The system checks the `email_logs` table to prevent duplicates
   - If you need to resend an email, delete the corresponding entry from `email_logs`

3. **Docker Desktop is required error**
   - This error appears when trying to run functions locally
   - For deployment, you don't need Docker installed
   - Make sure you're using the `deploy` command, not `serve`

3. **Edge Function errors**
   - The system includes fallback mechanisms to use the existing email queue
   - Check the Supabase logs for detailed error messages

#### Viewing Function Status

```bash
# List all deployed functions and their status
npx supabase functions list
```

#### Viewing Email Logs

You can view email logs in the Supabase Table Editor or with this query:

```sql
SELECT
  email_logs.id,
  email_logs.email_type,
  email_logs.recipient,
  email_logs.status,
  email_logs.sent_at,
  orders.id as order_id
FROM email_logs
JOIN orders ON email_logs.order_id = orders.id
ORDER BY email_logs.sent_at DESC;
```

### Security Considerations

- The Edge Function requires authentication to prevent unauthorized use
- Email templates do not contain sensitive information
- SMTP credentials are stored as Supabase secrets, not in the codebase
- Row Level Security ensures users can only see their own email logs
