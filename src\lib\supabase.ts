import { createClient } from '@supabase/supabase-js';

// Get environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Define the configuration options
const supabaseOptions = {
  auth: {
    persistSession: true,
    storageKey: 'badhees-auth-token',
    autoRefreshToken: true,
    detectSessionInUrl: true
  },
  global: {
    // Disable health checks to prevent 404 errors in console
    fetch: (...args: Parameters<typeof fetch>) => {
      const [url, options] = args;
      // Skip health check requests
      if (typeof url === 'string' && url.includes('health-check')) {
        return Promise.resolve(new Response(JSON.stringify([{ up: true }]), {
          headers: { 'Content-Type': 'application/json' },
          status: 200
        }));
      }
      return fetch(...args);
    }
  }
};

// Check for missing environment variables
if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables - using fallback values that will fail gracefully');

  // Log the actual values (masked for security)
  console.error(`
    Debug Info:
    - VITE_SUPABASE_URL: ${supabaseUrl ? `${supabaseUrl.substring(0, 8)}...` : 'undefined'}
    - VITE_SUPABASE_ANON_KEY: ${supabaseAnonKey ? 'Set (hidden)' : 'undefined'}
  `);

  // In development, show a more helpful error
  if (import.meta.env.DEV) {
    console.error(`
      ⚠️ Supabase environment variables are missing!

      Make sure you have the following in your .env file:
      VITE_SUPABASE_URL=your-project-url
      VITE_SUPABASE_ANON_KEY=your-anon-key

      These environment variables need to be set in your hosting platform's configuration.
    `);
  }
} else if (import.meta.env.DEV) {
  // In development, confirm that environment variables are loaded correctly
  console.log('✅ Supabase environment variables loaded successfully');
  console.log('Supabase URL:', supabaseUrl);
  console.log('Supabase Key (first 20 chars):', supabaseAnonKey?.substring(0, 20) + '...');
}

// Create and export the Supabase client
// We're using the actual values even if they're undefined, so the error will be more obvious
export const supabase = createClient(
  supabaseUrl || 'https://missing-supabase-url.supabase.co',
  supabaseAnonKey || 'missing-supabase-anon-key',
  supabaseOptions
);
