-- ============================================================================
-- TEST FRONTEND QUERY
-- This script tests the exact query that the frontend uses
-- ============================================================================

SELECT 'TESTING FRONTEND PRODUCT QUERY' as title;
SELECT '===============================' as separator;

-- ============================================================================
-- STEP 1: TEST BASIC PRODUCTS QUERY
-- ============================================================================

SELECT 'STEP 1: Testing basic products query' as step;

-- This is the core query from productService.ts
SELECT 
  p.*,
  c.id as category_id_from_join,
  c.name as category_name
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
ORDER BY p.created_at DESC;

-- ============================================================================
-- STEP 2: TEST PRODUCT IMAGES QUERY
-- ============================================================================

SELECT 'STEP 2: Testing product images query' as step;

-- Check if product_images table exists and has data
SELECT 
  pi.product_id,
  pi.image_url,
  pi.is_primary,
  p.name as product_name
FROM product_images pi
JOIN products p ON pi.product_id = p.id
ORDER BY p.name, pi.sort_order;

-- ============================================================================
-- STEP 3: TEST COMPLETE FRONTEND QUERY
-- ============================================================================

SELECT 'STEP 3: Testing complete frontend query (simplified)' as step;

-- This mimics the frontend query but without the rating_summary view
-- which might not exist and is causing the 400 errors
SELECT 
  p.id,
  p.name,
  p.description,
  p.price,
  p.sale_price,
  p.is_sale,
  p.is_new,
  p.is_featured,
  p.category_id,
  p.status,
  p.stock,
  p.sku,
  p.customization_available,
  p.specifications,
  p.rating,
  p.review_count,
  p.created_at,
  p.updated_at,
  -- Category info
  c.id as category_id_joined,
  c.name as category_name,
  -- Primary image
  pi.image_url as primary_image_url,
  pi.alt_text as primary_image_alt
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = true
WHERE p.status = 'active'
ORDER BY p.created_at DESC;

-- ============================================================================
-- STEP 4: TEST PRODUCT IMAGES AGGREGATION
-- ============================================================================

SELECT 'STEP 4: Testing product images aggregation' as step;

-- Get all images for each product (this is what the frontend expects)
SELECT 
  p.id as product_id,
  p.name as product_name,
  COALESCE(
    json_agg(
      json_build_object(
        'id', pi.id,
        'image_url', pi.image_url,
        'alt_text', pi.alt_text,
        'is_primary', pi.is_primary,
        'sort_order', pi.sort_order
      ) ORDER BY pi.sort_order
    ) FILTER (WHERE pi.id IS NOT NULL),
    '[]'::json
  ) as images
FROM products p
LEFT JOIN product_images pi ON p.id = pi.product_id
WHERE p.status = 'active'
GROUP BY p.id, p.name
ORDER BY p.name;

-- ============================================================================
-- STEP 5: TEST WHAT FRONTEND MAPPER EXPECTS
-- ============================================================================

SELECT 'STEP 5: Testing data format for frontend mapper' as step;

-- This query provides data in the exact format the mapSupabaseProductToFrontend expects
SELECT 
  p.id,
  p.name,
  p.description,
  p.price,
  p.sale_price,
  p.is_sale,
  p.is_new,
  p.is_featured,
  p.category_id,
  p.status,
  p.stock,
  p.sku,
  p.customization_available,
  p.specifications,
  p.rating,
  p.review_count,
  p.created_at,
  p.updated_at,
  -- Category as object (what frontend expects)
  CASE 
    WHEN c.id IS NOT NULL THEN 
      json_build_object('id', c.id, 'name', c.name)
    ELSE NULL
  END as category,
  -- Images as array (what frontend expects)
  COALESCE(
    (
      SELECT json_agg(
        json_build_object(
          'id', pi.id,
          'image_url', pi.image_url,
          'alt_text', pi.alt_text,
          'is_primary', pi.is_primary,
          'sort_order', pi.sort_order
        ) ORDER BY pi.sort_order
      )
      FROM product_images pi 
      WHERE pi.product_id = p.id
    ),
    '[]'::json
  ) as images
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
WHERE p.status = 'active'
ORDER BY p.created_at DESC
LIMIT 5;

-- ============================================================================
-- STEP 6: CHECK FOR POTENTIAL ISSUES
-- ============================================================================

SELECT 'STEP 6: Checking for potential issues' as step;

-- Check for products without categories
SELECT 'Products without categories:' as issue_check;
SELECT 
  p.id,
  p.name,
  p.category_id,
  CASE 
    WHEN c.id IS NULL THEN '❌ MISSING CATEGORY'
    ELSE '✅ HAS CATEGORY'
  END as category_status
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
WHERE p.status = 'active';

-- Check for products without images
SELECT 'Products without images:' as issue_check;
SELECT 
  p.id,
  p.name,
  CASE 
    WHEN pi.product_id IS NULL THEN '❌ NO IMAGES'
    ELSE '✅ HAS IMAGES'
  END as image_status
FROM products p
LEFT JOIN product_images pi ON p.id = pi.product_id
WHERE p.status = 'active'
GROUP BY p.id, p.name, pi.product_id;

-- Check for missing required columns
SELECT 'Required columns check:' as issue_check;
SELECT 
  CASE 
    WHEN EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'is_sale') THEN '✅'
    ELSE '❌'
  END as has_is_sale,
  CASE 
    WHEN EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'is_new') THEN '✅'
    ELSE '❌'
  END as has_is_new,
  CASE 
    WHEN EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'is_featured') THEN '✅'
    ELSE '❌'
  END as has_is_featured,
  CASE 
    WHEN EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'status') THEN '✅'
    ELSE '❌'
  END as has_status,
  CASE 
    WHEN EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'stock') THEN '✅'
    ELSE '❌'
  END as has_stock;

-- ============================================================================
-- STEP 7: FINAL RECOMMENDATIONS
-- ============================================================================

SELECT 'STEP 7: Recommendations' as step;

DO $$
DECLARE
  active_products_count integer;
  products_with_images_count integer;
  products_with_categories_count integer;
  missing_columns_count integer;
BEGIN
  -- Count active products
  SELECT COUNT(*) FROM products WHERE status = 'active' INTO active_products_count;
  
  -- Count products with images
  SELECT COUNT(DISTINCT product_id) FROM product_images INTO products_with_images_count;
  
  -- Count products with valid categories
  SELECT COUNT(*) FROM products p JOIN categories c ON p.category_id = c.id WHERE p.status = 'active' INTO products_with_categories_count;
  
  -- Count missing required columns
  SELECT 
    CASE WHEN NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'is_sale') THEN 1 ELSE 0 END +
    CASE WHEN NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'is_new') THEN 1 ELSE 0 END +
    CASE WHEN NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'is_featured') THEN 1 ELSE 0 END +
    CASE WHEN NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'status') THEN 1 ELSE 0 END +
    CASE WHEN NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'stock') THEN 1 ELSE 0 END
  INTO missing_columns_count;
  
  RAISE NOTICE '=== FRONTEND COMPATIBILITY CHECK ===';
  RAISE NOTICE 'Active products: %', active_products_count;
  RAISE NOTICE 'Products with images: %', products_with_images_count;
  RAISE NOTICE 'Products with categories: %', products_with_categories_count;
  RAISE NOTICE 'Missing required columns: %', missing_columns_count;
  RAISE NOTICE '';
  
  IF missing_columns_count > 0 THEN
    RAISE NOTICE '🔥 ISSUE: Missing required columns for frontend';
    RAISE NOTICE '📋 ACTION: Run fix_existing_products_targeted.sql';
  ELSIF active_products_count = 0 THEN
    RAISE NOTICE '🔥 ISSUE: No active products';
    RAISE NOTICE '📋 ACTION: Update product status to active';
  ELSIF products_with_images_count = 0 THEN
    RAISE NOTICE '🔥 ISSUE: No product images';
    RAISE NOTICE '📋 ACTION: Add product images';
  ELSIF products_with_categories_count < active_products_count THEN
    RAISE NOTICE '🔥 ISSUE: Some products missing categories';
    RAISE NOTICE '📋 ACTION: Fix category assignments';
  ELSE
    RAISE NOTICE '✅ FRONTEND COMPATIBILITY: GOOD';
    RAISE NOTICE '📋 ACTION: Products should be visible on website';
  END IF;
END $$;

SELECT 'FRONTEND QUERY TEST COMPLETE' as result;
