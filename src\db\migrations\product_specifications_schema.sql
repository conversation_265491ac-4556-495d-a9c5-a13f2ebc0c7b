-- Check if specifications column exists in products table
DO $$
BEGIN
  IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'specifications') THEN
    -- Add specifications column to products table
    ALTER TABLE products ADD COLUMN specifications JSONB DEFAULT '{}'::jsonb;

    -- Add comment to explain the column
    COMMENT ON COLUMN products.specifications IS 'Product specifications stored as key-value pairs in JSONB format';

    -- Update existing products to have empty specifications if needed
    UPDATE products SET specifications = '{}'::jsonb WHERE specifications IS NULL;
  END IF;
END
$$;

-- Create an index on the specifications column for better performance
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_products_specifications') THEN
    CREATE INDEX idx_products_specifications ON products USING GIN (specifications);
  END IF;
END
$$;

-- Add a comment to explain the purpose of this migration
COMMENT ON TABLE products IS 'Products table with specifications stored as JSONB';
