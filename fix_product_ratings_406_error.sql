-- Fix 406 errors for product_ratings_summary view
-- This script addresses the RLS and permission issues causing 406 errors

-- Step 1: Check if the view exists and recreate it with proper permissions
DROP VIEW IF EXISTS product_ratings_summary CASCADE;

-- Create the product_ratings_summary view
CREATE VIEW product_ratings_summary AS
SELECT 
  product_id,
  COALESCE(ROUND(AVG(rating)::NUMERIC, 1), 0) AS average_rating,
  COUNT(*) AS review_count
FROM product_reviews
GROUP BY product_id;

-- Step 2: Grant explicit permissions on the view
-- Grant SELECT permission to anonymous users (for public product viewing)
GRANT SELECT ON product_ratings_summary TO anon;

-- Grant SELECT permission to authenticated users
GRANT SELECT ON product_ratings_summary TO authenticated;

-- Step 3: Ensure the underlying product_reviews table has proper RLS
-- Check if RLS is enabled on product_reviews
ALTER TABLE product_reviews ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Anyone can view product reviews" ON product_reviews;
DROP POLICY IF EXISTS "Authenticated users can insert reviews" ON product_reviews;
DROP POLICY IF EXISTS "Users can update their own reviews" ON product_reviews;
DROP POLICY IF EXISTS "Users can delete their own reviews" ON product_reviews;

-- Create comprehensive RLS policies for product_reviews
-- Allow everyone (including anonymous users) to view product reviews
CREATE POLICY "Public can view product reviews"
  ON product_reviews FOR SELECT
  TO anon, authenticated
  USING (true);

-- Allow authenticated users to insert their own reviews
CREATE POLICY "Authenticated users can insert reviews"
  ON product_reviews FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Allow users to update their own reviews
CREATE POLICY "Users can update their own reviews"
  ON product_reviews FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- Allow users to delete their own reviews
CREATE POLICY "Users can delete their own reviews"
  ON product_reviews FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Step 4: Create a security definer function for the view access
-- This ensures the view can be accessed even with RLS enabled
CREATE OR REPLACE FUNCTION get_product_ratings_summary()
RETURNS TABLE (
  product_id UUID,
  average_rating NUMERIC,
  review_count BIGINT
) 
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    pr.product_id,
    COALESCE(ROUND(AVG(pr.rating)::NUMERIC, 1), 0) AS average_rating,
    COUNT(*)::BIGINT AS review_count
  FROM product_reviews pr
  GROUP BY pr.product_id;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION get_product_ratings_summary() TO anon, authenticated;

-- Step 5: Ensure proper schema permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon, authenticated;

-- Step 6: Add helpful comments
COMMENT ON VIEW product_ratings_summary IS 'Public view showing product rating summaries - accessible to all users';
COMMENT ON FUNCTION get_product_ratings_summary IS 'Security definer function to access product ratings summary';

-- Step 7: Verify the fix by testing the view
-- This should not cause any errors
DO $$
DECLARE
  test_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO test_count FROM product_ratings_summary;
  RAISE NOTICE 'product_ratings_summary view is working. Found % products with ratings.', test_count;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error testing product_ratings_summary view: %', SQLERRM;
END;
$$;
