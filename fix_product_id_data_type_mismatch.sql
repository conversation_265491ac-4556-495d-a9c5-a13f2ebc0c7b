-- FIX FOR PRODUCT ID DATA TYPE MISMATCH
-- This script fixes the UUID vs INTEGER mismatch in product_reviews table

-- Step 1: Check current data types
SELECT 
  'products.id data type:' as info,
  data_type 
FROM information_schema.columns 
WHERE table_name = 'products' AND column_name = 'id';

SELECT 
  'product_reviews.product_id data type:' as info,
  data_type 
FROM information_schema.columns 
WHERE table_name = 'product_reviews' AND column_name = 'product_id';

-- Step 2: Check if products table uses INTEGER or UUID
DO $$
DECLARE
  products_id_type TEXT;
  reviews_product_id_type TEXT;
  sample_product_id TEXT;
BEGIN
  -- Get the data types
  SELECT data_type INTO products_id_type
  FROM information_schema.columns 
  WHERE table_name = 'products' AND column_name = 'id';
  
  SELECT data_type INTO reviews_product_id_type
  FROM information_schema.columns 
  WHERE table_name = 'product_reviews' AND column_name = 'product_id';
  
  RAISE NOTICE 'Products table ID type: %', products_id_type;
  RAISE NOTICE 'Product reviews product_id type: %', reviews_product_id_type;
  
  -- Get a sample product ID to see the actual format
  SELECT id::TEXT INTO sample_product_id FROM products LIMIT 1;
  RAISE NOTICE 'Sample product ID: %', sample_product_id;
  
  -- If types don't match, we need to fix this
  IF products_id_type != reviews_product_id_type THEN
    RAISE NOTICE 'DATA TYPE MISMATCH DETECTED! Need to fix product_reviews table.';
  ELSE
    RAISE NOTICE 'Data types match - the issue might be elsewhere.';
  END IF;
END;
$$;

-- Step 3: Fix the product_reviews table to match products table
-- First, let's backup any existing reviews (if any)
CREATE TABLE IF NOT EXISTS product_reviews_backup AS 
SELECT * FROM product_reviews;

-- Drop the problematic view and table
DROP VIEW IF EXISTS product_ratings_summary CASCADE;
DROP TABLE IF EXISTS product_reviews CASCADE;

-- Step 4: Recreate product_reviews table with correct data type
-- Check what type the products table actually uses
DO $$
DECLARE
  products_id_type TEXT;
BEGIN
  SELECT data_type INTO products_id_type
  FROM information_schema.columns 
  WHERE table_name = 'products' AND column_name = 'id';
  
  IF products_id_type = 'integer' OR products_id_type = 'bigint' THEN
    -- Products uses INTEGER, so create reviews table with INTEGER
    EXECUTE '
    CREATE TABLE product_reviews (
      id SERIAL PRIMARY KEY,
      product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
      user_id UUID REFERENCES auth.users(id),
      rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
      comment TEXT,
      title TEXT,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );';
    RAISE NOTICE 'Created product_reviews table with INTEGER product_id';
  ELSE
    -- Products uses UUID, so create reviews table with UUID
    EXECUTE '
    CREATE TABLE product_reviews (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      product_id UUID REFERENCES products(id) ON DELETE CASCADE,
      user_id UUID REFERENCES auth.users(id),
      rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
      comment TEXT,
      title TEXT,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );';
    RAISE NOTICE 'Created product_reviews table with UUID product_id';
  END IF;
END;
$$;

-- Step 5: Create indexes
CREATE INDEX idx_product_reviews_product_id ON product_reviews(product_id);
CREATE INDEX idx_product_reviews_user_id ON product_reviews(user_id);

-- Step 6: Enable RLS and create policies
ALTER TABLE product_reviews ENABLE ROW LEVEL SECURITY;

-- Create policies for public access
CREATE POLICY "Public can view product reviews"
  ON product_reviews FOR SELECT
  USING (true);

CREATE POLICY "Authenticated users can insert reviews"
  ON product_reviews FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own reviews"
  ON product_reviews FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own reviews"
  ON product_reviews FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Step 7: Create the rating summary view with correct data type
DO $$
DECLARE
  products_id_type TEXT;
BEGIN
  SELECT data_type INTO products_id_type
  FROM information_schema.columns 
  WHERE table_name = 'products' AND column_name = 'id';
  
  IF products_id_type = 'integer' OR products_id_type = 'bigint' THEN
    -- Create view for INTEGER product_id
    EXECUTE '
    CREATE VIEW product_ratings_summary AS
    SELECT 
      product_id,
      COALESCE(ROUND(AVG(rating)::NUMERIC, 1), 0) AS average_rating,
      COUNT(*) AS review_count
    FROM product_reviews
    GROUP BY product_id;';
    
    -- Create RPC function for INTEGER
    EXECUTE '
    CREATE OR REPLACE FUNCTION get_product_rating(input_product_id INTEGER)
    RETURNS JSON
    SECURITY DEFINER
    SET search_path = public
    LANGUAGE plpgsql
    AS $func$
    DECLARE
      result JSON;
    BEGIN
      SELECT json_build_object(
        ''product_id'', input_product_id,
        ''average_rating'', COALESCE(ROUND(AVG(rating)::NUMERIC, 1), 0),
        ''review_count'', COUNT(*)
      )
      INTO result
      FROM product_reviews
      WHERE product_id = input_product_id;
      
      IF result IS NULL THEN
        result := json_build_object(
          ''product_id'', input_product_id,
          ''average_rating'', 0,
          ''review_count'', 0
        );
      END IF;
      
      RETURN result;
    END;
    $func$;';
    
    RAISE NOTICE 'Created INTEGER-based rating functions';
  ELSE
    -- Create view for UUID product_id
    EXECUTE '
    CREATE VIEW product_ratings_summary AS
    SELECT 
      product_id,
      COALESCE(ROUND(AVG(rating)::NUMERIC, 1), 0) AS average_rating,
      COUNT(*) AS review_count
    FROM product_reviews
    GROUP BY product_id;';
    
    -- Create RPC function for UUID
    EXECUTE '
    CREATE OR REPLACE FUNCTION get_product_rating(input_product_id UUID)
    RETURNS JSON
    SECURITY DEFINER
    SET search_path = public
    LANGUAGE plpgsql
    AS $func$
    DECLARE
      result JSON;
    BEGIN
      SELECT json_build_object(
        ''product_id'', input_product_id,
        ''average_rating'', COALESCE(ROUND(AVG(rating)::NUMERIC, 1), 0),
        ''review_count'', COUNT(*)
      )
      INTO result
      FROM product_reviews
      WHERE product_id = input_product_id;
      
      IF result IS NULL THEN
        result := json_build_object(
          ''product_id'', input_product_id,
          ''average_rating'', 0,
          ''review_count'', 0
        );
      END IF;
      
      RETURN result;
    END;
    $func$;';
    
    RAISE NOTICE 'Created UUID-based rating functions';
  END IF;
END;
$$;

-- Step 8: Grant permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON product_reviews TO anon, authenticated;
GRANT INSERT, UPDATE, DELETE ON product_reviews TO authenticated;
GRANT SELECT ON product_ratings_summary TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_product_rating TO anon, authenticated;

-- Step 9: Test the fix
DO $$
DECLARE
  test_product_id TEXT;
  test_count INTEGER;
BEGIN
  -- Get a sample product ID
  SELECT id::TEXT INTO test_product_id FROM products LIMIT 1;
  
  IF test_product_id IS NOT NULL THEN
    -- Test the view
    EXECUTE format('SELECT COUNT(*) FROM product_ratings_summary WHERE product_id = %L', test_product_id) INTO test_count;
    RAISE NOTICE 'SUCCESS: View query works for product_id %', test_product_id;
    
    RAISE NOTICE '=== FIX COMPLETED SUCCESSFULLY ===';
    RAISE NOTICE 'Product reviews table recreated with correct data types';
    RAISE NOTICE 'Rating summary view and functions are working';
    RAISE NOTICE 'Test your frontend now - 400 errors should be resolved';
  ELSE
    RAISE NOTICE 'No products found in database - please add some products first';
  END IF;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error during testing: %', SQLERRM;
END;
$$;
