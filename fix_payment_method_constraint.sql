-- Fix Payment Method Constraint Issue
-- This script fixes the payment method constraint violation by updating existing data first

-- Step 1: Update existing payment_method values to match new constraints
UPDATE orders SET payment_method = 'cash_on_delivery' WHERE payment_method = 'Cash on Delivery';
UPDATE orders SET payment_method = 'online_payment' WHERE payment_method = 'Online Payment';
UPDATE orders SET payment_method = 'online_payment' WHERE payment_method = 'razorpay';
UPDATE orders SET payment_method = 'cash_on_delivery' WHERE payment_method IS NULL OR payment_method = '';
UPDATE orders SET payment_method = 'cash_on_delivery' WHERE payment_method NOT IN ('cash_on_delivery', 'online_payment', 'razorpay', 'upi', 'card', 'netbanking', 'wallet');

-- Step 2: Remove existing constraint if any
ALTER TABLE orders DROP CONSTRAINT IF EXISTS orders_payment_method_check;

-- Step 3: Add the new constraint
ALTER TABLE orders ADD CONSTRAINT orders_payment_method_check 
  CHECK (payment_method IN ('cash_on_delivery', 'online_payment', 'razorpay', 'upi', 'card', 'netbanking', 'wallet'));

-- Step 4: Add missing columns if they don't exist
DO $$
BEGIN
  -- Add payment_status column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'orders' AND column_name = 'payment_status'
  ) THEN
    ALTER TABLE orders ADD COLUMN payment_status TEXT CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')) DEFAULT 'pending';
  END IF;

  -- Add payment_reference column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'orders' AND column_name = 'payment_reference'
  ) THEN
    ALTER TABLE orders ADD COLUMN payment_reference TEXT;
  END IF;

  -- Add payment_details column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'orders' AND column_name = 'payment_details'
  ) THEN
    ALTER TABLE orders ADD COLUMN payment_details JSONB;
  END IF;
END $$;

-- Step 5: Set default payment_status for existing orders
UPDATE orders SET payment_status = 'pending' WHERE payment_status IS NULL;

-- Step 6: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_orders_payment_status ON orders(payment_status);
CREATE INDEX IF NOT EXISTS idx_orders_payment_reference ON orders(payment_reference);

-- Verify the changes
SELECT 
  payment_method, 
  payment_status, 
  COUNT(*) as count 
FROM orders 
GROUP BY payment_method, payment_status 
ORDER BY payment_method, payment_status;
