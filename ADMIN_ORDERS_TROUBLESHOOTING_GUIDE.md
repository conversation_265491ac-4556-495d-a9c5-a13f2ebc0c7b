# Admin Orders Troubleshooting Guide

## Current Issues Identified

Based on the console errors, we have multiple problems:

1. **500 Server Errors** - Database queries are failing at the server level
2. **View Not Found** - `orders_with_user_info` view doesn't exist
3. **Join Failures** - Foreign key relationships not working
4. **No Data Display** - Orders management page shows empty

## Step-by-Step Fix Process

### Step 1: Execute Database Fix (CRITICAL)

**Execute this in Supabase SQL Editor:**

```sql
-- Copy and paste the entire content of step_by_step_admin_orders_fix.sql
-- This will run diagnostics and create the necessary tables step by step
```

**What this does:**
- ✅ Checks current database structure
- ✅ Creates `user_profiles` table with simple structure
- ✅ Populates it with sample data for existing orders
- ✅ Sets up basic permissions (no RLS complications)
- ✅ Creates a simple view for orders with user info
- ✅ Tests everything step by step

### Step 2: Verify Database Setup

After running the SQL script, check these in Supabase:

1. **Tables Tab**: Verify `user_profiles` table exists
2. **SQL Editor**: Run this test query:
   ```sql
   SELECT COUNT(*) FROM orders;
   SELECT COUNT(*) FROM user_profiles;
   SELECT COUNT(*) FROM orders_with_user_info;
   ```
3. **Expected Results**: All queries should return numbers, not errors

### Step 3: Test Frontend Changes (Already Applied)

The order service has been simplified to:
- ✅ Start with basic queries first
- ✅ Fetch orders and order items separately
- ✅ Avoid complex joins that cause 500 errors
- ✅ Provide detailed logging for debugging

### Step 4: Clear Browser Cache

1. **Hard Refresh**: Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)
2. **Clear Console**: Click the clear button in browser console
3. **Reload Page**: Navigate to `/admin/orders` again

## Expected Results After Fix

### Console Logs Should Show:
```
[OrderService] Fetching all orders with status filter: undefined
[OrderService] Attempting basic orders query...
[OrderService] Found X orders, now fetching order items...
[OrderService] Successfully processed X orders
```

### Admin Orders Page Should Display:
- ✅ List of orders with basic information
- ✅ Customer names as "Customer ID: xxxxxxxx"
- ✅ Order status, payment method, total amount
- ✅ No 500 errors in console

## Troubleshooting Common Issues

### Issue: "Table 'user_profiles' doesn't exist"
**Solution**: Re-run the `step_by_step_admin_orders_fix.sql` script

### Issue: "Permission denied" errors
**Solution**: The script disables RLS temporarily. If still getting errors:
```sql
-- Run this in Supabase SQL Editor
GRANT ALL ON user_profiles TO anon, authenticated;
GRANT ALL ON orders TO anon, authenticated;
```

### Issue: Still getting 500 errors
**Solution**: Check Supabase logs:
1. Go to Supabase Dashboard
2. Click "Logs" in sidebar
3. Look for recent errors
4. Share the specific error messages

### Issue: Orders show but no order items
**Solution**: Check if `order_items` table exists and has data:
```sql
SELECT COUNT(*) FROM order_items;
SELECT * FROM order_items LIMIT 5;
```

## Advanced Debugging

### Check Database Structure:
```sql
-- Check if all required tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('orders', 'order_items', 'products', 'user_profiles');

-- Check orders table structure
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'orders';
```

### Test Basic Queries:
```sql
-- Test each table individually
SELECT COUNT(*) as orders_count FROM orders;
SELECT COUNT(*) as items_count FROM order_items;
SELECT COUNT(*) as products_count FROM products;
SELECT COUNT(*) as profiles_count FROM user_profiles;
```

## Recovery Plan

If the above steps don't work:

### Option 1: Minimal Working Version
```sql
-- Create the absolute minimum needed
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID PRIMARY KEY,
  display_name TEXT DEFAULT 'Customer'
);

-- Insert basic data
INSERT INTO user_profiles (id, display_name)
SELECT DISTINCT user_id, 'Customer ' || SUBSTRING(user_id::text, 1, 8)
FROM orders
ON CONFLICT (id) DO NOTHING;
```

### Option 2: Frontend-Only Solution
If database fixes don't work, we can modify the frontend to work without user profiles entirely.

## Contact Information

If you continue to have issues:

1. **Share the exact error messages** from Supabase logs
2. **Provide the output** of the diagnostic SQL queries
3. **Screenshot** of the Supabase tables tab showing what tables exist

## Success Indicators

✅ **Database**: SQL script runs without errors
✅ **Tables**: `orders`, `order_items`, `user_profiles` all exist
✅ **View**: `orders_with_user_info` view works
✅ **Frontend**: Orders page loads and shows data
✅ **Console**: No 500 errors, only success messages

The key is to start simple and build up complexity only after the basics work.
