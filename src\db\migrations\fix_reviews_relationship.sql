-- Fix the relationship between product_reviews and user_profiles tables

-- First, check if the foreign key relationship exists
DO $$
BEGIN
  -- Check if the user_profiles column exists in product_reviews
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'product_reviews'
    AND column_name = 'user_profiles'
  ) THEN
    -- If it doesn't exist, we're good - no need to drop it
    RAISE NOTICE 'No user_profiles column found in product_reviews table. This is correct.';
  END IF;
END $$;

-- Make sure the user_profiles table exists and has the correct structure
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_name = 'user_profiles'
  ) THEN
    CREATE TABLE user_profiles (
      id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
      display_name TEXT,
      first_name TEXT,
      last_name TEXT,
      email TEXT,
      role TEXT CHECK (role IN ('user', 'admin')) DEFAULT 'user',
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    RAISE NOTICE 'Created user_profiles table';
  ELSE
    -- Make sure first_name and last_name columns exist
    IF NOT EXISTS (
      SELECT 1
      FROM information_schema.columns
      WHERE table_name = 'user_profiles'
      AND column_name = 'first_name'
    ) THEN
      ALTER TABLE user_profiles ADD COLUMN first_name TEXT;
      RAISE NOTICE 'Added first_name column to user_profiles';
    END IF;

    IF NOT EXISTS (
      SELECT 1
      FROM information_schema.columns
      WHERE table_name = 'user_profiles'
      AND column_name = 'last_name'
    ) THEN
      ALTER TABLE user_profiles ADD COLUMN last_name TEXT;
      RAISE NOTICE 'Added last_name column to user_profiles';
    END IF;
  END IF;
END $$;

-- Note: We can't use pg_reload_conf() in Supabase as it requires superuser privileges
-- Instead, we'll rely on Supabase's automatic schema cache updates

-- Create a function to update product ratings in the products table
CREATE OR REPLACE FUNCTION update_product_rating()
RETURNS TRIGGER AS $$
DECLARE
  avg_rating NUMERIC;
  review_count BIGINT;
BEGIN
  -- Get the average rating and review count
  SELECT
    COALESCE(ROUND(AVG(rating)::NUMERIC, 1), 0),
    COUNT(*)
  INTO
    avg_rating,
    review_count
  FROM product_reviews
  WHERE product_id = COALESCE(NEW.product_id, OLD.product_id);

  -- Update the products table with the new rating information
  BEGIN
    UPDATE products
    SET
      rating = avg_rating,
      review_count = review_count
    WHERE id = COALESCE(NEW.product_id, OLD.product_id);
  EXCEPTION WHEN OTHERS THEN
    -- If the update fails (e.g., columns don't exist), log the error but don't fail the transaction
    RAISE NOTICE 'Failed to update product rating: %', SQLERRM;
  END;

  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Drop existing triggers if they exist
DROP TRIGGER IF EXISTS update_product_rating_insert ON product_reviews;
DROP TRIGGER IF EXISTS update_product_rating_update ON product_reviews;
DROP TRIGGER IF EXISTS update_product_rating_delete ON product_reviews;

-- Create triggers to update product ratings when reviews change
CREATE TRIGGER update_product_rating_insert
AFTER INSERT ON product_reviews
FOR EACH ROW
EXECUTE FUNCTION update_product_rating();

CREATE TRIGGER update_product_rating_update
AFTER UPDATE ON product_reviews
FOR EACH ROW
EXECUTE FUNCTION update_product_rating();

CREATE TRIGGER update_product_rating_delete
AFTER DELETE ON product_reviews
FOR EACH ROW
EXECUTE FUNCTION update_product_rating();

-- Add comments to explain the functions
COMMENT ON FUNCTION update_product_rating IS 'Updates product rating in the products table when reviews change';
