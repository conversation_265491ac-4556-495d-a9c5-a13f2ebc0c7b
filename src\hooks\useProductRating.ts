/**
 * Simplified Product Rating Hook - REBUILT TO FIX INFINITE LOOPS
 *
 * This hook provides product rating data with minimal API calls and no real-time updates
 * to prevent infinite loops.
 */
import { useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';
import {
  getProductRatingSummary,
  ProductRatingSummary
} from '@/services/productReviewsService';

// Simplified query keys for product ratings
export const productRatingKeys = {
  all: ['product-ratings'] as const,
  rating: (productId: string) => [...productRatingKeys.all, productId] as const,
};

/**
 * Simplified hook to get product rating - NO REAL-TIME UPDATES
 * @param productId The product ID to get rating for
 * @returns Rating data with loading and error states
 */
export function useProductRating(productId: string) {
  // Memoize the query key to prevent unnecessary re-renders
  const queryKey = useMemo(() => productRatingKeys.rating(productId), [productId]);

  // Simple query with aggressive caching to prevent infinite loops
  const query = useQuery({
    queryKey,
    queryFn: async () => {
      if (!productId) {
        return { averageRating: 0, reviewCount: 0 };
      }
      console.log(`[ProductRating] Fetching rating for product: ${productId}`);
      return getProductRatingSummary(productId);
    },
    enabled: !!productId,
    staleTime: 30 * 60 * 1000, // 30 minutes - very long cache
    gcTime: 60 * 60 * 1000, // 1 hour - very long garbage collection
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: false, // No retries to prevent loops
    refetchInterval: false,
    refetchIntervalInBackground: false,
  });

  return {
    rating: query.data?.averageRating || 0,
    reviewCount: query.data?.reviewCount || 0,
    isLoading: query.isLoading,
    error: query.error,
    refetch: query.refetch,
  };
}

// REMOVED: Complex bulk rating hooks that were causing infinite loops
// If bulk ratings are needed in the future, they should be implemented
// with a different approach that doesn't cause infinite loops
