-- Create search functions for The Badhees e-commerce site

-- Function to search products with full-text search
CREATE OR REPLACE FUNCTION search_products(search_query TEXT)
RETURNS SETOF products AS $$
BEGIN
  -- If search query is empty, return empty set
  IF search_query IS NULL OR search_query = '' THEN
    RETURN;
  END IF;

  -- Convert spaces to & for tsquery
  -- This allows searching for multiple words (e.g. "wooden chair" becomes "wooden & chair")
  RETURN QUERY
  SELECT p.*
  FROM products p
  LEFT JOIN categories c ON p.category_id = c.id
  WHERE 
    -- Only include active products in search results
    p.status = 'active' AND
    -- Full-text search on product name, description, and category name
    to_tsvector('english', 
      p.name || ' ' || 
      COALESCE(p.description, '') || ' ' || 
      COALESCE(c.name, '') || ' ' ||
      COALESCE(p.sku, '')
    ) @@ to_tsquery('english', 
      regexp_replace(
        regexp_replace(search_query, '(\w+)', '\1:*', 'g'),  -- Add :* for prefix matching
        '\s+', ' & ', 'g'                                    -- Replace spaces with &
      )
    )
  ORDER BY 
    -- Prioritize matches in name over description
    ts_rank(to_tsvector('english', p.name), to_tsquery('english', 
      regexp_replace(
        regexp_replace(search_query, '(\w+)', '\1:*', 'g'),
        '\s+', ' & ', 'g'
      )
    )) DESC,
    -- Then by whether it's featured
    p.is_featured DESC,
    -- Then by whether it's on sale
    p.is_sale DESC,
    -- Then by name
    p.name ASC;
END;
$$ LANGUAGE plpgsql;

-- Function to get search suggestions based on product names
CREATE OR REPLACE FUNCTION get_search_suggestions(prefix TEXT, limit_count INTEGER DEFAULT 5)
RETURNS TABLE (suggestion TEXT) AS $$
BEGIN
  IF prefix IS NULL OR prefix = '' OR length(prefix) < 2 THEN
    RETURN;
  END IF;

  RETURN QUERY
  SELECT DISTINCT p.name
  FROM products p
  WHERE 
    p.status = 'active' AND
    p.name ILIKE prefix || '%'
  ORDER BY p.name
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Table to store search history
CREATE TABLE IF NOT EXISTS search_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  query TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index on query for faster lookups
CREATE INDEX IF NOT EXISTS search_history_query_idx ON search_history(query);

-- Function to get popular searches
CREATE OR REPLACE FUNCTION get_popular_searches(limit_count INTEGER DEFAULT 5)
RETURNS TABLE (query TEXT, count BIGINT) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    search_history.query,
    COUNT(*) as count
  FROM search_history
  WHERE created_at > NOW() - INTERVAL '30 days'
  GROUP BY search_history.query
  ORDER BY count DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get user's recent searches
CREATE OR REPLACE FUNCTION get_user_recent_searches(user_uuid UUID, limit_count INTEGER DEFAULT 5)
RETURNS TABLE (query TEXT, created_at TIMESTAMP WITH TIME ZONE) AS $$
BEGIN
  IF user_uuid IS NULL THEN
    RETURN;
  END IF;

  RETURN QUERY
  SELECT 
    search_history.query,
    search_history.created_at
  FROM search_history
  WHERE user_id = user_uuid
  ORDER BY created_at DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Add RLS policies for search_history table
ALTER TABLE search_history ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to insert their own search history
CREATE POLICY insert_own_search_history ON search_history
  FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Policy to allow users to select their own search history
CREATE POLICY select_own_search_history ON search_history
  FOR SELECT TO authenticated
  USING (auth.uid() = user_id);

-- Policy to allow admins to select all search history
CREATE POLICY admin_select_all_search_history ON search_history
  FOR SELECT TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE user_profiles.id = auth.uid() AND user_profiles.role = 'admin'
    )
  );
