-- VERIFICATION SCRIPT FOR PRODUCT RATINGS FIX
-- Run this after executing comprehensive_product_ratings_fix.sql

-- Test 1: Check if view exists and is accessible
SELECT 'TEST 1: View Accessibility' as test_name;
SELECT COUNT(*) as total_products_with_ratings FROM product_ratings_summary;

-- Test 2: Check if RPC function works
SELECT 'TEST 2: RPC Function' as test_name;
SELECT COUNT(*) as total_via_function FROM get_product_ratings_summary();

-- Test 3: Test specific product rating (replace with actual product ID)
SELECT 'TEST 3: Specific Product Rating' as test_name;
SELECT * FROM product_ratings_summary LIMIT 3;

-- Test 4: Test RPC for specific product (you'll need to replace with actual product ID)
SELECT 'TEST 4: RPC for Specific Product' as test_name;
-- Uncomment and replace with actual product ID:
-- SELECT get_product_rating('your-product-id-here'::UUID);

-- Test 5: Check permissions
SELECT 'TEST 5: Permissions Check' as test_name;
SELECT 
  grantee,
  privilege_type,
  is_grantable
FROM information_schema.table_privileges 
WHERE table_name = 'product_ratings_summary';

-- Test 6: Check RLS policies
SELECT 'TEST 6: RLS Policies' as test_name;
SELECT 
  policyname,
  permissive,
  roles,
  cmd
FROM pg_policies 
WHERE tablename = 'product_reviews';

-- Test 7: Simulate anonymous access
SELECT 'TEST 7: Anonymous Access Simulation' as test_name;
SET ROLE anon;
SELECT COUNT(*) as accessible_to_anon FROM product_ratings_summary;
RESET ROLE;

-- Final status
SELECT 'VERIFICATION COMPLETE' as status, 
       'If all tests above show results without errors, the fix is successful!' as message;
