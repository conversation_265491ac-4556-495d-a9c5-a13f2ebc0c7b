-- Fix for infinite recursion in RLS policies
-- This script specifically addresses the "infinite recursion detected in policy for relation user_profiles" error

-- First, drop all existing policies on user_profiles to start fresh
DROP POLICY IF EXISTS "Users can view their own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON user_profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can insert profiles" ON user_profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can delete profiles" ON user_profiles;
DROP POLICY IF EXISTS "Allow anonymous count of user_profiles" ON user_profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON user_profiles;
DROP POLICY IF EXISTS "Public read access" ON user_profiles;

-- Drop any functions that might be causing recursion
DROP FUNCTION IF EXISTS is_admin(uuid);
DROP FUNCTION IF EXISTS check_if_admin(uuid);
DROP FUNCTION IF EXISTS get_user_role(uuid);

-- Create a simple admin check function that doesn't query user_profiles table
-- This avoids the recursion by using a direct query to auth.users
CREATE OR REPLACE FUNCTION is_admin_safe(user_id uuid)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if the user has the admin role in their metadata
  -- This avoids querying the user_profiles table which would cause recursion
  RETURN EXISTS (
    SELECT 1 
    FROM auth.users 
    WHERE id = user_id 
    AND (
      raw_user_meta_data->>'role' = 'admin' OR 
      raw_app_meta_data->>'role' = 'admin'
    )
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create simple, non-recursive policies

-- 1. Allow all authenticated users to read all user profiles
-- This is a simple policy that doesn't cause recursion
CREATE POLICY "Public read access"
  ON user_profiles FOR SELECT
  TO authenticated
  USING (true);

-- 2. Allow users to update only their own profile
CREATE POLICY "Users can update their own profile"
  ON user_profiles FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

-- 3. Allow users to insert their own profile
CREATE POLICY "Users can insert their own profile"
  ON user_profiles FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

-- 4. Allow admins to update any profile
CREATE POLICY "Admins can update all profiles"
  ON user_profiles FOR UPDATE
  TO authenticated
  USING (is_admin_safe(auth.uid()));

-- 5. Allow admins to insert any profile
CREATE POLICY "Admins can insert profiles"
  ON user_profiles FOR INSERT
  TO authenticated
  WITH CHECK (is_admin_safe(auth.uid()));

-- 6. Allow admins to delete any profile
CREATE POLICY "Admins can delete profiles"
  ON user_profiles FOR DELETE
  TO authenticated
  USING (is_admin_safe(auth.uid()));

-- Create a function to ensure a user profile exists without causing recursion
CREATE OR REPLACE FUNCTION ensure_user_profile_safe(user_id uuid)
RETURNS boolean AS $$
BEGIN
  -- Check if profile exists
  IF EXISTS (SELECT 1 FROM user_profiles WHERE id = user_id) THEN
    RETURN true;
  ELSE
    -- Get user data
    DECLARE
      user_email text;
      user_name text;
      is_user_admin boolean;
    BEGIN
      SELECT 
        email, 
        raw_user_meta_data->>'name',
        (raw_user_meta_data->>'role' = 'admin' OR raw_app_meta_data->>'role' = 'admin')
      INTO 
        user_email, 
        user_name,
        is_user_admin
      FROM auth.users
      WHERE id = user_id;
      
      -- Insert new profile
      INSERT INTO user_profiles (
        id,
        display_name,
        email,
        role,
        created_at,
        updated_at
      ) VALUES (
        user_id,
        COALESCE(user_name, split_part(user_email, '@', 1)),
        user_email,
        CASE WHEN is_user_admin THEN 'admin' ELSE 'user' END,
        now(),
        now()
      );
      
      RETURN true;
    EXCEPTION
      WHEN OTHERS THEN
        RAISE WARNING 'Failed to create profile for user %: %', user_id, SQLERRM;
        RETURN false;
    END;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to update a user profile without causing recursion
CREATE OR REPLACE FUNCTION update_user_profile_safe(
  user_id uuid,
  p_display_name text DEFAULT NULL,
  p_phone text DEFAULT NULL,
  p_dob date DEFAULT NULL,
  p_street text DEFAULT NULL,
  p_city text DEFAULT NULL,
  p_state text DEFAULT NULL,
  p_postal_code text DEFAULT NULL,
  p_country text DEFAULT NULL,
  p_avatar_url text DEFAULT NULL
)
RETURNS boolean AS $$
BEGIN
  -- Ensure profile exists
  PERFORM ensure_user_profile_safe(user_id);
  
  -- Update profile
  UPDATE user_profiles
  SET
    display_name = COALESCE(p_display_name, display_name),
    phone = COALESCE(p_phone, phone),
    dob = COALESCE(p_dob, dob),
    street = COALESCE(p_street, street),
    city = COALESCE(p_city, city),
    state = COALESCE(p_state, state),
    postal_code = COALESCE(p_postal_code, postal_code),
    country = COALESCE(p_country, country),
    avatar_url = COALESCE(p_avatar_url, avatar_url),
    updated_at = now()
  WHERE id = user_id;
  
  RETURN true;
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Failed to update profile for user %: %', user_id, SQLERRM;
    RETURN false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fix the handle_new_user function to avoid recursion
CREATE OR REPLACE FUNCTION public.handle_new_user() 
RETURNS TRIGGER AS $$
BEGIN
  -- Check if a profile already exists for this user
  -- This prevents duplicate key errors
  IF EXISTS (
    SELECT 1 FROM public.user_profiles WHERE id = new.id
  ) THEN
    -- Profile already exists, just update it
    UPDATE public.user_profiles
    SET 
      display_name = COALESCE(new.raw_user_meta_data->>'name', split_part(new.email, '@', 1)),
      email = new.email,
      updated_at = now()
    WHERE id = new.id;
  ELSE
    -- Insert new profile
    INSERT INTO public.user_profiles (
      id, 
      display_name, 
      email, 
      role,
      created_at,
      updated_at
    )
    VALUES (
      new.id, 
      COALESCE(new.raw_user_meta_data->>'name', split_part(new.email, '@', 1)),
      new.email,
      CASE 
        WHEN new.raw_user_meta_data->>'role' = 'admin' OR new.raw_app_meta_data->>'role' = 'admin' 
        THEN 'admin' 
        ELSE 'user' 
      END,
      now(),
      now()
    );
  END IF;
  
  RETURN new;
EXCEPTION
  WHEN others THEN
    -- Log the error but don't prevent user creation
    RAISE WARNING 'Error in handle_new_user trigger: %', SQLERRM;
    RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Make sure the trigger is set up
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO postgres, anon, authenticated, service_role;
