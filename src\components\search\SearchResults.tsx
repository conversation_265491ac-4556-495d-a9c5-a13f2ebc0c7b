import React from 'react';
import { Link } from 'react-router-dom';
import { FrontendProduct } from '@/services/product/types';
import { formatPrice } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ArrowRight, Search } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface SearchResultsProps {
  /**
   * Search results to display
   */
  results: FrontendProduct[];

  /**
   * Current search query
   */
  query: string;

  /**
   * Whether the search is currently loading
   */
  isLoading: boolean;

  /**
   * Callback when a result is selected
   */
  onResultSelect?: (product: FrontendProduct) => void;

  /**
   * Callback when "View all results" is clicked
   */
  onViewAllResults?: () => void;

  /**
   * Maximum number of results to display
   */
  maxResults?: number;

  /**
   * Additional CSS classes
   */
  className?: string;

  /**
   * Whether to show the "View all results" button
   */
  showViewAll?: boolean;
}

/**
 * A component to display search results
 */
export const SearchResults: React.FC<SearchResultsProps> = ({
  results,
  query,
  isLoading,
  onResultSelect,
  onViewAllResults,
  maxResults = 5,
  className = "",
  showViewAll = true,
}) => {
  // Limit the number of results to display
  const displayedResults = results.slice(0, maxResults);
  const hasMoreResults = results.length > maxResults;

  // Handle result click
  const handleResultClick = (product: FrontendProduct) => {
    if (onResultSelect) {
      onResultSelect(product);
    }
  };

  // Handle "View all results" click
  const handleViewAllClick = () => {
    if (onViewAllResults) {
      onViewAllResults();
    }
  };

  // If loading, show loading state
  if (isLoading) {
    return (
      <div className={cn("p-4 bg-white shadow-md rounded-md", className)}>
        <div className="flex items-center justify-center py-8">
          <div className="animate-pulse flex flex-col space-y-4 w-full">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-badhees-100 rounded"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-badhees-100 rounded w-3/4"></div>
                  <div className="h-4 bg-badhees-100 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // If no results and query is not empty, show no results message
  if (displayedResults.length === 0 && query.trim()) {
    return (
      <div className={cn("p-4 bg-white shadow-md rounded-md", className)}>
        <div className="flex flex-col items-center justify-center py-8 text-center">
          <Search className="h-12 w-12 text-badhees-200 mb-4" />
          <h3 className="text-lg font-medium text-badhees-800 mb-2">No results found</h3>
          <p className="text-badhees-500 mb-4">
            We couldn't find any products matching "{query}"
          </p>
          <div className="text-sm text-badhees-500">
            Try using different keywords or check for spelling mistakes
          </div>
        </div>
      </div>
    );
  }

  // If no query, don't show anything
  if (!query.trim()) {
    return null;
  }

  // Otherwise, show results
  return (
    <div className={cn("bg-white shadow-md rounded-md overflow-hidden", className)}>
      <div className="p-2">
        {displayedResults.map((product) => (
          <Link
            key={product.id}
            to={`/products/${product.id}`}
            className="flex items-center p-2 hover:bg-badhees-50 rounded-md transition-colors"
            onClick={() => handleResultClick(product)}
          >
            <div className="w-16 h-16 bg-badhees-100 rounded overflow-hidden flex-shrink-0">
              {product.image ? (
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover"
                  loading="lazy"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    target.nextElementSibling?.classList.remove('hidden');
                  }}
                />
              ) : null}
              <div className={`w-full h-full flex items-center justify-center bg-badhees-100 text-badhees-400 ${product.image ? 'hidden' : ''}`}>
                <span className="text-xs">No image</span>
              </div>
            </div>
            <div className="ml-4 flex-1">
              <h4 className="text-sm font-medium text-badhees-800 line-clamp-1">{product.name}</h4>
              <p className="text-xs text-badhees-500 line-clamp-1">{product.category}</p>
              <div className="flex items-center mt-1">
                <span className="text-sm font-medium text-badhees-800">
                  {formatPrice(product.price)}
                </span>
                {product.isSale && product.salePrice && (
                  <span className="ml-2 text-xs line-through text-badhees-400">
                    {formatPrice(product.price)}
                  </span>
                )}
              </div>
            </div>
          </Link>
        ))}
      </div>

      {showViewAll && query.trim() && (hasMoreResults || results.length > 0) && (
        <div className="p-2 border-t border-badhees-100">
          <Button
            variant="ghost"
            className="w-full justify-center py-2 text-badhees-accent hover:text-badhees-accent-hover hover:bg-badhees-50"
            onClick={handleViewAllClick}
          >
            <span>View all results</span>
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  );
};
