/**
 * Search Service
 * 
 * This module provides functions for searching products and managing search history.
 */
import { supabase } from '@/lib/supabase';
import { FrontendProduct } from '@/services/product/types';
import { mapSupabaseProductToFrontend } from '@/services/product/mappers';
import { handleSupabaseError } from '@/utils/supabaseHelpers';

/**
 * Search for products using server-side full-text search
 * @param query Search query
 * @returns Array of products matching the search query
 */
export const searchProducts = async (query: string): Promise<FrontendProduct[]> => {
  if (!query || !query.trim()) return [];
  
  try {
    // Save search query to history if user is authenticated
    saveSearchQuery(query).catch(err => console.error('Error saving search query:', err));
    
    // Call the search_products function
    const { data, error } = await supabase
      .rpc('search_products', { search_query: query })
      .select(`
        *,
        category:categories(id, name),
        images:product_images(*),
        rating_summary:product_ratings_summary!product_id(*)
      `);
      
    if (error) {
      console.error('Search error:', error);
      return [];
    }
    
    if (!data || data.length === 0) {
      console.log(`No results found for query: "${query}"`);
      return [];
    }
    
    // Map Supabase products to frontend format
    return data.map(mapSupabaseProductToFrontend);
  } catch (error) {
    handleSupabaseError(error, 'searchProducts');
    return [];
  }
};

/**
 * Get search suggestions based on product names
 * @param prefix Search prefix
 * @param limit Maximum number of suggestions to return
 * @returns Array of search suggestions
 */
export const getSearchSuggestions = async (prefix: string, limit: number = 5): Promise<string[]> => {
  if (!prefix || prefix.length < 2) return [];
  
  try {
    const { data, error } = await supabase
      .rpc('get_search_suggestions', { 
        prefix, 
        limit_count: limit 
      });
      
    if (error) {
      console.error('Error getting search suggestions:', error);
      return [];
    }
    
    return data.map(item => item.suggestion);
  } catch (error) {
    handleSupabaseError(error, 'getSearchSuggestions');
    return [];
  }
};

/**
 * Save a search query to the user's search history
 * @param query Search query
 */
export const saveSearchQuery = async (query: string): Promise<void> => {
  if (!query || !query.trim()) return;
  
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return; // Only save search history for authenticated users
    
    await supabase
      .from('search_history')
      .insert({
        user_id: user.id,
        query: query.trim()
      });
  } catch (error) {
    console.error('Error saving search query:', error);
    // Don't throw - this is a non-critical operation
  }
};

/**
 * Get popular searches
 * @param limit Maximum number of popular searches to return
 * @returns Array of popular search queries with counts
 */
export const getPopularSearches = async (limit: number = 5): Promise<{ query: string, count: number }[]> => {
  try {
    const { data, error } = await supabase
      .rpc('get_popular_searches', { 
        limit_count: limit 
      });
      
    if (error) {
      console.error('Error getting popular searches:', error);
      return [];
    }
    
    return data.map(item => ({
      query: item.query,
      count: Number(item.count)
    }));
  } catch (error) {
    handleSupabaseError(error, 'getPopularSearches');
    return [];
  }
};

/**
 * Get user's recent searches
 * @param limit Maximum number of recent searches to return
 * @returns Array of recent search queries with timestamps
 */
export const getUserRecentSearches = async (limit: number = 5): Promise<{ query: string, timestamp: Date }[]> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return []; // Only get search history for authenticated users
    
    const { data, error } = await supabase
      .rpc('get_user_recent_searches', { 
        user_uuid: user.id,
        limit_count: limit 
      });
      
    if (error) {
      console.error('Error getting user recent searches:', error);
      return [];
    }
    
    return data.map(item => ({
      query: item.query,
      timestamp: new Date(item.created_at)
    }));
  } catch (error) {
    handleSupabaseError(error, 'getUserRecentSearches');
    return [];
  }
};
