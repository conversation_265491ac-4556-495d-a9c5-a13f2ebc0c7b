-- Enhanced Orders and Payment Tracking System
-- This migration enhances the orders system with comprehensive payment tracking

-- First, ensure the orders table has all necessary payment-related columns
DO $$
BEGIN
  -- Add payment_status column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'orders' AND column_name = 'payment_status'
  ) THEN
    ALTER TABLE orders ADD COLUMN payment_status TEXT CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')) DEFAULT 'pending';
  END IF;

  -- Add razorpay_order_id column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'orders' AND column_name = 'razorpay_order_id'
  ) THEN
    ALTER TABLE orders ADD COLUMN razorpay_order_id TEXT;
  END IF;

  -- Add razorpay_payment_id column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'orders' AND column_name = 'razorpay_payment_id'
  ) THEN
    ALTER TABLE orders ADD COLUMN razorpay_payment_id TEXT;
  END IF;

  -- Add payment_reference column for transaction/reference numbers
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'orders' AND column_name = 'payment_reference'
  ) THEN
    ALTER TABLE orders ADD COLUMN payment_reference TEXT;
  END IF;

  -- Add payment_details JSONB column for storing additional payment information
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'orders' AND column_name = 'payment_details'
  ) THEN
    ALTER TABLE orders ADD COLUMN payment_details JSONB;
  END IF;

  -- Update existing payment_method values to match new constraints
  UPDATE orders SET payment_method = 'cash_on_delivery' WHERE payment_method = 'Cash on Delivery';
  UPDATE orders SET payment_method = 'online_payment' WHERE payment_method = 'Online Payment';
  UPDATE orders SET payment_method = 'online_payment' WHERE payment_method = 'razorpay';
  UPDATE orders SET payment_method = 'cash_on_delivery' WHERE payment_method IS NULL OR payment_method = '';

  -- Update payment_method column to have better constraints
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'orders' AND column_name = 'payment_method'
  ) THEN
    -- Remove existing constraint if any
    ALTER TABLE orders DROP CONSTRAINT IF EXISTS orders_payment_method_check;
    -- Add new constraint
    ALTER TABLE orders ADD CONSTRAINT orders_payment_method_check
      CHECK (payment_method IN ('cash_on_delivery', 'online_payment', 'razorpay', 'upi', 'card', 'netbanking', 'wallet'));
  END IF;

END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_orders_payment_status ON orders(payment_status);
CREATE INDEX IF NOT EXISTS idx_orders_razorpay_order_id ON orders(razorpay_order_id);
CREATE INDEX IF NOT EXISTS idx_orders_razorpay_payment_id ON orders(razorpay_payment_id);
CREATE INDEX IF NOT EXISTS idx_orders_payment_reference ON orders(payment_reference);

-- Create a comprehensive payment_transactions table for detailed tracking
CREATE TABLE IF NOT EXISTS payment_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  transaction_type TEXT NOT NULL CHECK (transaction_type IN ('payment', 'refund', 'partial_refund')),
  payment_method TEXT NOT NULL,
  payment_gateway TEXT, -- razorpay, stripe, etc.
  gateway_transaction_id TEXT, -- razorpay_payment_id, stripe_payment_intent_id, etc.
  gateway_order_id TEXT, -- razorpay_order_id, etc.
  amount DECIMAL(10, 2) NOT NULL,
  currency TEXT NOT NULL DEFAULT 'INR',
  status TEXT NOT NULL CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded')),
  gateway_response JSONB, -- Full response from payment gateway
  failure_reason TEXT,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for payment_transactions
CREATE INDEX IF NOT EXISTS idx_payment_transactions_order_id ON payment_transactions(order_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_gateway_transaction_id ON payment_transactions(gateway_transaction_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_status ON payment_transactions(status);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_user_id ON payment_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_created_at ON payment_transactions(created_at);

-- Enable RLS on payment_transactions
ALTER TABLE payment_transactions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for payment_transactions
-- Users can view their own payment transactions
CREATE POLICY "Users can view own payment transactions" ON payment_transactions
  FOR SELECT
  USING (auth.uid() = user_id);

-- Admins can view all payment transactions
CREATE POLICY "Admins can view all payment transactions" ON payment_transactions
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Service role can manage all payment transactions
CREATE POLICY "Service role can manage payment transactions" ON payment_transactions
  FOR ALL
  USING (auth.role() = 'service_role');

-- Create function to update payment transaction timestamps
CREATE OR REPLACE FUNCTION update_payment_transaction_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for payment_transactions updated_at
DROP TRIGGER IF EXISTS update_payment_transactions_updated_at ON payment_transactions;
CREATE TRIGGER update_payment_transactions_updated_at
  BEFORE UPDATE ON payment_transactions
  FOR EACH ROW
  EXECUTE FUNCTION update_payment_transaction_updated_at();

-- Create function to automatically update order payment status when payment transaction is updated
CREATE OR REPLACE FUNCTION sync_order_payment_status()
RETURNS TRIGGER AS $$
BEGIN
  -- Update order payment status based on the latest successful payment transaction
  IF NEW.status = 'completed' AND NEW.transaction_type = 'payment' THEN
    UPDATE orders
    SET
      payment_status = 'paid',
      razorpay_payment_id = CASE WHEN NEW.payment_gateway = 'razorpay' THEN NEW.gateway_transaction_id ELSE razorpay_payment_id END,
      razorpay_order_id = CASE WHEN NEW.payment_gateway = 'razorpay' THEN NEW.gateway_order_id ELSE razorpay_order_id END,
      payment_reference = NEW.gateway_transaction_id,
      payment_details = COALESCE(payment_details, '{}'::jsonb) || jsonb_build_object(
        'gateway', NEW.payment_gateway,
        'transaction_id', NEW.gateway_transaction_id,
        'payment_method', NEW.payment_method,
        'completed_at', NEW.updated_at
      ),
      updated_at = NOW()
    WHERE id = NEW.order_id;
  ELSIF NEW.status = 'failed' AND NEW.transaction_type = 'payment' THEN
    UPDATE orders
    SET
      payment_status = 'failed',
      payment_details = COALESCE(payment_details, '{}'::jsonb) || jsonb_build_object(
        'failure_reason', NEW.failure_reason,
        'failed_at', NEW.updated_at
      ),
      updated_at = NOW()
    WHERE id = NEW.order_id;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to sync order payment status
DROP TRIGGER IF EXISTS sync_order_payment_status_trigger ON payment_transactions;
CREATE TRIGGER sync_order_payment_status_trigger
  AFTER INSERT OR UPDATE ON payment_transactions
  FOR EACH ROW
  EXECUTE FUNCTION sync_order_payment_status();

-- Grant necessary permissions
GRANT ALL ON payment_transactions TO postgres, anon, authenticated, service_role;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO postgres, anon, authenticated, service_role;
