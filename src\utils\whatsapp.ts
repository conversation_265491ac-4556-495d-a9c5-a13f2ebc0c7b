/**
 * WhatsApp Utility Functions
 * 
 * This module provides utility functions for WhatsApp integration
 */

// Business WhatsApp number
export const BUSINESS_WHATSAPP_NUMBER = "918197705438";

// Default messages for different contexts
export const WHATSAPP_MESSAGES = {
  general: "Hello! I'm interested in your furniture and interior design services.",
  product: (productName: string) => `Hello! I'm interested in the ${productName}. Could you please provide more details?`,
  customProject: "Hello! I'd like to discuss a custom interior design project. Can we schedule a consultation?",
  support: "Hello! I need help with my order. Could you please assist me?",
  consultation: "Hello! I'd like to book a consultation for my interior design project.",
  quote: "Hello! I'd like to get a quote for furniture and interior design services."
};

/**
 * Opens WhatsApp with a pre-filled message
 * @param message - The message to send
 * @param phoneNumber - The phone number to send to (optional, defaults to business number)
 */
export const openWhatsApp = (
  message: string = WHATSAPP_MESSAGES.general,
  phoneNumber: string = BUSINESS_WHATSAPP_NUMBER
): void => {
  const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
  window.open(whatsappUrl, '_blank');
};

/**
 * Opens WhatsApp for product inquiry
 * @param productName - Name of the product
 * @param phoneNumber - The phone number to send to (optional)
 */
export const openWhatsAppForProduct = (
  productName: string,
  phoneNumber: string = BUSINESS_WHATSAPP_NUMBER
): void => {
  const message = WHATSAPP_MESSAGES.product(productName);
  openWhatsApp(message, phoneNumber);
};

/**
 * Opens WhatsApp for custom project inquiry
 * @param phoneNumber - The phone number to send to (optional)
 */
export const openWhatsAppForCustomProject = (
  phoneNumber: string = BUSINESS_WHATSAPP_NUMBER
): void => {
  openWhatsApp(WHATSAPP_MESSAGES.customProject, phoneNumber);
};

/**
 * Opens WhatsApp for support
 * @param phoneNumber - The phone number to send to (optional)
 */
export const openWhatsAppForSupport = (
  phoneNumber: string = BUSINESS_WHATSAPP_NUMBER
): void => {
  openWhatsApp(WHATSAPP_MESSAGES.support, phoneNumber);
};

/**
 * Opens WhatsApp for consultation booking
 * @param phoneNumber - The phone number to send to (optional)
 */
export const openWhatsAppForConsultation = (
  phoneNumber: string = BUSINESS_WHATSAPP_NUMBER
): void => {
  openWhatsApp(WHATSAPP_MESSAGES.consultation, phoneNumber);
};

/**
 * Opens WhatsApp for quote request
 * @param phoneNumber - The phone number to send to (optional)
 */
export const openWhatsAppForQuote = (
  phoneNumber: string = BUSINESS_WHATSAPP_NUMBER
): void => {
  openWhatsApp(WHATSAPP_MESSAGES.quote, phoneNumber);
};

/**
 * Formats phone number for WhatsApp (removes spaces, dashes, and adds country code if needed)
 * @param phoneNumber - The phone number to format
 * @returns Formatted phone number
 */
export const formatWhatsAppNumber = (phoneNumber: string): string => {
  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  // If number doesn't start with country code, assume it's Indian number
  if (cleaned.length === 10) {
    return `91${cleaned}`;
  }
  
  // If it already has country code
  if (cleaned.length === 12 && cleaned.startsWith('91')) {
    return cleaned;
  }
  
  // Return as is for other formats
  return cleaned;
};

/**
 * Validates if a phone number is valid for WhatsApp
 * @param phoneNumber - The phone number to validate
 * @returns True if valid, false otherwise
 */
export const isValidWhatsAppNumber = (phoneNumber: string): boolean => {
  const formatted = formatWhatsAppNumber(phoneNumber);
  
  // Check if it's a valid length (10-15 digits)
  return formatted.length >= 10 && formatted.length <= 15;
};
