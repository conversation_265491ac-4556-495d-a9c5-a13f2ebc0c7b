-- Improved RLS policies for user_profiles table
-- This script fixes the recursive RLS issues and implements proper role-based access control

-- First, drop all existing policies on the user_profiles table
DROP POLICY IF EXISTS "Users can view their own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON user_profiles;
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all profiles" ON user_profiles;
DROP POLICY IF EXISTS "<PERSON><PERSON> can update all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can insert profiles" ON user_profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can delete profiles" ON user_profiles;

-- Create a more efficient function to check if a user is an admin
-- This avoids recursion by using a direct query with security definer
CREATE OR REPLACE FUNCTION is_admin(user_id uuid)
RETURNS BOOLEAN AS $$
DECLARE
  user_role TEXT;
BEGIN
  -- Direct query to get the role
  SELECT role INTO user_role FROM user_profiles WHERE id = user_id;
  RETURN user_role = 'admin';
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to handle new user signup
-- This ensures every new user gets a profile with the correct role
CREATE OR REPLACE FUNCTION public.handle_new_user() 
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_profiles (
    id, 
    display_name, 
    email, 
    role,
    created_at,
    updated_at
  )
  VALUES (
    new.id, 
    COALESCE(new.raw_user_meta_data->>'name', split_part(new.email, '@', 1)),
    new.email,
    'user',  -- Default role is always 'user'
    now(),
    now()
  );
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create or replace the trigger for new user signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Create new, simplified policies that avoid recursion

-- 1. Allow users to view their own profile
CREATE POLICY "Users can view their own profile" 
  ON user_profiles FOR SELECT 
  USING (auth.uid() = id);

-- 2. Allow users to update their own profile (except role field)
CREATE POLICY "Users can update their own profile" 
  ON user_profiles FOR UPDATE 
  USING (auth.uid() = id)
  WITH CHECK (
    auth.uid() = id AND
    (role IS NULL OR role = (SELECT role FROM user_profiles WHERE id = auth.uid()))
  );

-- 3. Allow admins to view all profiles
CREATE POLICY "Admins can view all profiles" 
  ON user_profiles FOR SELECT 
  USING (is_admin(auth.uid()));

-- 4. Allow admins to update all profiles
CREATE POLICY "Admins can update all profiles" 
  ON user_profiles FOR UPDATE 
  USING (is_admin(auth.uid()));

-- 5. Allow admins to insert new profiles
CREATE POLICY "Admins can insert profiles" 
  ON user_profiles FOR INSERT 
  WITH CHECK (is_admin(auth.uid()));

-- 6. Allow admins to delete profiles
CREATE POLICY "Admins can delete profiles" 
  ON user_profiles FOR DELETE 
  USING (is_admin(auth.uid()));

-- Create a function to safely set a user as admin
-- This is safer than direct SQL updates
CREATE OR REPLACE FUNCTION set_user_as_admin(admin_email TEXT)
RETURNS TEXT AS $$
DECLARE
  user_id UUID;
  result TEXT;
BEGIN
  -- Find the user ID from the email
  SELECT id INTO user_id FROM auth.users WHERE email = admin_email;
  
  IF user_id IS NULL THEN
    RETURN 'User not found with email: ' || admin_email;
  END IF;
  
  -- Update the user's role to admin
  UPDATE user_profiles
  SET role = 'admin'
  WHERE id = user_id;
  
  RETURN 'User set as admin: ' || admin_email;
EXCEPTION
  WHEN OTHERS THEN
    RETURN 'Error setting user as admin: ' || SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Example usage (uncomment to use):
-- SELECT set_user_as_admin('<EMAIL>');
