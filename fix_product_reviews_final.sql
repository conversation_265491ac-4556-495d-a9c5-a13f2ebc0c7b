-- FINAL FIX FOR PRODUCT R<PERSON><PERSON>EWS SYSTEM
-- This script addresses the specific join query error and relationship issues

-- Step 1: Ensure user_profiles table exists and has correct structure
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_name = 'user_profiles'
  ) THEN
    CREATE TABLE user_profiles (
      id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
      display_name TEXT,
      first_name TEXT,
      last_name TEXT,
      email TEXT,
      role TEXT CHECK (role IN ('user', 'admin')) DEFAULT 'user',
      phone TEXT,
      dob DATE,
      street TEXT,
      city TEXT,
      state TEXT,
      postal_code TEXT,
      country TEXT,
      avatar_url TEXT,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    RAISE NOTICE 'Created user_profiles table';
  ELSE
    RAISE NOTICE 'user_profiles table already exists';
  END IF;
END $$;

-- Step 2: Fix product_reviews table and its relationships
DO $$
BEGIN
  -- Check if product_reviews table exists
  IF NOT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_name = 'product_reviews'
  ) THEN
    -- Create the table with correct foreign key
    CREATE TABLE product_reviews (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      product_id UUID REFERENCES products(id) ON DELETE CASCADE,
      user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
      rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
      comment TEXT,
      title TEXT,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    -- Add indexes
    CREATE INDEX idx_product_reviews_product_id ON product_reviews(product_id);
    CREATE INDEX idx_product_reviews_user_id ON product_reviews(user_id);
    CREATE INDEX idx_product_reviews_created_at ON product_reviews(created_at);
    
    RAISE NOTICE 'Created product_reviews table with correct foreign keys';
  ELSE
    RAISE NOTICE 'product_reviews table already exists - checking foreign key relationships';
    
    -- Check and fix the foreign key relationship to user_profiles
    BEGIN
      -- First, try to drop any existing constraint that might be wrong
      ALTER TABLE product_reviews DROP CONSTRAINT IF EXISTS product_reviews_user_id_fkey;
      
      -- Add the correct foreign key constraint
      ALTER TABLE product_reviews 
      ADD CONSTRAINT product_reviews_user_id_fkey 
      FOREIGN KEY (user_id) REFERENCES user_profiles(id) ON DELETE CASCADE;
      
      RAISE NOTICE 'Fixed foreign key relationship between product_reviews and user_profiles';
    EXCEPTION WHEN OTHERS THEN
      RAISE NOTICE 'Foreign key relationship may already be correct or there are data issues: %', SQLERRM;
    END;
  END IF;
END $$;

-- Step 3: Enable RLS and create policies
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_reviews ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Anyone can view product reviews" ON product_reviews;
DROP POLICY IF EXISTS "Authenticated users can insert reviews" ON product_reviews;
DROP POLICY IF EXISTS "Users can update their own reviews" ON product_reviews;
DROP POLICY IF EXISTS "Users can delete their own reviews" ON product_reviews;
DROP POLICY IF EXISTS "Anyone can view user profiles" ON user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON user_profiles;

-- Create policies for product_reviews
CREATE POLICY "Anyone can view product reviews"
  ON product_reviews FOR SELECT
  USING (true);

CREATE POLICY "Authenticated users can insert reviews"
  ON product_reviews FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own reviews"
  ON product_reviews FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own reviews"
  ON product_reviews FOR DELETE
  USING (auth.uid() = user_id);

-- Create policies for user_profiles
CREATE POLICY "Anyone can view user profiles"
  ON user_profiles FOR SELECT
  USING (true);

CREATE POLICY "Users can update their own profile"
  ON user_profiles FOR UPDATE
  USING (auth.uid() = id);

-- Step 4: Create or replace the has_user_purchased_product function
CREATE OR REPLACE FUNCTION has_user_purchased_product(
  input_user_id UUID,
  input_product_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  has_purchased BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM orders o
    JOIN order_items oi ON o.id = oi.order_id
    WHERE o.user_id = input_user_id
    AND oi.product_id = input_product_id
    AND o.status IN ('delivered', 'shipped')
  ) INTO has_purchased;
  
  RETURN COALESCE(has_purchased, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 5: Create views
DROP VIEW IF EXISTS product_ratings_summary CASCADE;
CREATE VIEW product_ratings_summary AS
SELECT 
  product_id,
  COALESCE(ROUND(AVG(rating)::NUMERIC, 1), 0) AS average_rating,
  COUNT(*) AS review_count
FROM product_reviews
GROUP BY product_id;

DROP VIEW IF EXISTS user_purchasable_reviews CASCADE;
CREATE VIEW user_purchasable_reviews AS
SELECT DISTINCT
  o.user_id,
  oi.product_id,
  p.name as product_name,
  o.id as order_id,
  o.created_at as purchase_date,
  CASE WHEN pr.id IS NULL THEN false ELSE true END as has_reviewed
FROM
  orders o
  JOIN order_items oi ON o.id = oi.order_id
  JOIN products p ON oi.product_id = p.id
  LEFT JOIN product_reviews pr ON pr.product_id = oi.product_id AND pr.user_id = o.user_id
WHERE
  o.status IN ('delivered', 'shipped');

-- Step 6: Create profiles for existing users who don't have one
INSERT INTO user_profiles (id, display_name, email, role, created_at, updated_at)
SELECT 
  id,
  COALESCE(raw_user_meta_data->>'name', split_part(email, '@', 1)),
  email,
  'user',
  now(),
  now()
FROM auth.users
WHERE NOT EXISTS (
  SELECT 1 FROM user_profiles WHERE user_profiles.id = auth.users.id
)
ON CONFLICT (id) DO NOTHING;

-- Step 7: Grant permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT INSERT, UPDATE, DELETE ON product_reviews TO authenticated;
GRANT UPDATE ON user_profiles TO authenticated;
GRANT EXECUTE ON FUNCTION has_user_purchased_product TO anon, authenticated;

-- Step 8: Add helpful comments
COMMENT ON FUNCTION has_user_purchased_product IS 'Checks if a user has purchased a specific product';
COMMENT ON VIEW product_ratings_summary IS 'Provides a summary of ratings for all products';
COMMENT ON VIEW user_purchasable_reviews IS 'Shows products a user has purchased and can review';

-- Final verification and success message
DO $$
BEGIN
  RAISE NOTICE '=== PRODUCT REVIEWS SYSTEM SETUP COMPLETED ===';
  RAISE NOTICE 'Tables: user_profiles, product_reviews';
  RAISE NOTICE 'Views: product_ratings_summary, user_purchasable_reviews';
  RAISE NOTICE 'Function: has_user_purchased_product';
  RAISE NOTICE 'Foreign key relationship between product_reviews and user_profiles established';
  RAISE NOTICE 'RLS policies configured for security';
  RAISE NOTICE '=== READY TO TEST ===';
END $$;
