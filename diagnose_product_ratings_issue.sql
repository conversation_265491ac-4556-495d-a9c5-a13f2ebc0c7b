-- Diagnostic script to identify and fix the 406 error issue with product_ratings_summary
-- Run this script in your Supabase SQL editor to diagnose the problem

-- Step 1: Check if the product_ratings_summary view exists
SELECT 
  schemaname, 
  viewname, 
  viewowner,
  definition
FROM pg_views 
WHERE viewname = 'product_ratings_summary';

-- Step 2: Check if the product_reviews table exists and has data
SELECT 
  COUNT(*) as total_reviews,
  COUNT(DISTINCT product_id) as products_with_reviews,
  MIN(created_at) as oldest_review,
  MAX(created_at) as newest_review
FROM product_reviews;

-- Step 3: Check RLS status on product_reviews table
SELECT 
  schemaname,
  tablename,
  rowsecurity as rls_enabled,
  hasrls as has_rls_policies
FROM pg_tables 
WHERE tablename = 'product_reviews';

-- Step 4: List all policies on product_reviews table
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE tablename = 'product_reviews';

-- Step 5: Check permissions on the view
SELECT 
  grantee,
  privilege_type,
  is_grantable
FROM information_schema.table_privileges 
WHERE table_name = 'product_ratings_summary';

-- Step 6: Test the view directly (this might fail with 406 error)
SELECT 
  product_id,
  average_rating,
  review_count
FROM product_ratings_summary 
LIMIT 5;

-- Step 7: Test the underlying query that the view uses
SELECT 
  product_id,
  COALESCE(ROUND(AVG(rating)::NUMERIC, 1), 0) AS average_rating,
  COUNT(*) AS review_count
FROM product_reviews
GROUP BY product_id
LIMIT 5;

-- Step 8: Check if there are any conflicting policies or functions
SELECT 
  routine_name,
  routine_type,
  security_type
FROM information_schema.routines 
WHERE routine_name LIKE '%product%rating%';

-- Step 9: Test access as different user types
-- This will show what the anon user can see
SET ROLE anon;
SELECT COUNT(*) FROM product_reviews;
RESET ROLE;

-- Step 10: Show current user and their permissions
SELECT current_user, current_role;

-- Step 11: Check if there are any triggers that might interfere
SELECT 
  trigger_name,
  event_manipulation,
  event_object_table,
  action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'product_reviews';
