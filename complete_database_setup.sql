-- ============================================================================
-- COMPLETE DATABASE SETUP FOR THE BADHEES E-COMMERCE SITE
-- This script will set up all necessary tables and populate them with sample data
-- ============================================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- STEP 1: DIAGNOSTIC CHECKS
-- ============================================================================

SELECT 'STEP 1: Running diagnostic checks' as step;

-- Check if tables exist
SELECT 
  CASE 
    WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'categories') 
    THEN 'EXISTS' 
    ELSE 'MISSING' 
  END as categories_table,
  CASE 
    WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'products') 
    THEN 'EXISTS' 
    ELSE 'MISSING' 
  END as products_table,
  CASE 
    WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'product_images') 
    THEN 'EXISTS' 
    ELSE 'MISSING' 
  END as product_images_table;

-- Check current data counts
SELECT 
  (SELECT COUNT(*) FROM categories) as category_count,
  (SELECT COUNT(*) FROM products) as product_count,
  (SELECT COUNT(*) FROM product_images) as image_count;

-- ============================================================================
-- STEP 2: CREATE CATEGORIES TABLE AND DATA
-- ============================================================================

SELECT 'STEP 2: Setting up categories' as step;

-- Create categories table if it doesn't exist
CREATE TABLE IF NOT EXISTS categories (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  image_url TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert sample categories
INSERT INTO categories (id, name, description, image_url) VALUES
  ('11111111-1111-1111-1111-111111111111', 'Sofas', 'Comfortable seating solutions for your living room', 'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'),
  ('*************-2222-2222-************', 'Chairs', 'Stylish and ergonomic chairs for every space', 'https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'),
  ('*************-3333-3333-************', 'Tables', 'Functional and beautiful tables for dining and work', 'https://images.unsplash.com/photo-1549497538-303791108f95?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'),
  ('*************-4444-4444-444444444444', 'Storage', 'Organize your space with our storage solutions', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'),
  ('55555555-5555-5555-5555-555555555555', 'Lighting', 'Illuminate your home with style', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'),
  ('66666666-6666-6666-6666-666666666666', 'Decor', 'Beautiful accessories to complete your space', 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80')
ON CONFLICT (id) DO NOTHING;

-- ============================================================================
-- STEP 3: CREATE PRODUCTS TABLE AND DATA
-- ============================================================================

SELECT 'STEP 3: Setting up products' as step;

-- Create products table if it doesn't exist
CREATE TABLE IF NOT EXISTS products (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  price DECIMAL(10,2) NOT NULL,
  sale_price DECIMAL(10,2),
  is_sale BOOLEAN DEFAULT false,
  is_new BOOLEAN DEFAULT false,
  is_featured BOOLEAN DEFAULT false,
  category_id UUID REFERENCES categories(id),
  status TEXT CHECK (status IN ('active', 'draft', 'deleted')) DEFAULT 'active',
  stock INTEGER DEFAULT 0,
  sku TEXT UNIQUE,
  customization_available BOOLEAN DEFAULT false,
  specifications JSONB DEFAULT '{}',
  rating DECIMAL(3,2) DEFAULT 0,
  review_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create product_images table if it doesn't exist
CREATE TABLE IF NOT EXISTS product_images (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  image_url TEXT NOT NULL,
  alt_text TEXT,
  is_primary BOOLEAN DEFAULT false,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert sample products
INSERT INTO products (id, name, description, price, sale_price, is_sale, is_new, is_featured, category_id, stock, sku, specifications) VALUES
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Comfort Lounge Chair', 'A luxurious lounge chair perfect for relaxation', 599.00, 499.00, true, false, true, '*************-2222-2222-************', 15, 'CLC-001', '{"Material": "Premium leather", "Dimensions": "32W x 34D x 30H inches", "Weight": "45 lbs", "Color": "Brown"}'),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Modern Dining Table', 'Sleek dining table for contemporary homes', 899.00, NULL, false, true, true, '*************-3333-3333-************', 8, 'MDT-002', '{"Material": "Oak wood", "Dimensions": "72W x 36D x 30H inches", "Seats": "6 people", "Finish": "Natural oak"}'),
  ('cccccccc-cccc-cccc-cccc-cccccccccccc', 'Executive Office Chair', 'Ergonomic chair for professional environments', 449.00, 399.00, true, false, false, '*************-2222-2222-************', 25, 'EOC-003', '{"Material": "Mesh and fabric", "Adjustable": "Height, armrests, lumbar", "Weight capacity": "300 lbs", "Warranty": "5 years"}'),
  ('dddddddd-dddd-dddd-dddd-dddddddddddd', 'Scandinavian Sofa', 'Minimalist 3-seater sofa with clean lines', 1299.00, NULL, false, true, true, '11111111-1111-1111-1111-111111111111', 5, 'SS-004', '{"Material": "Linen fabric", "Dimensions": "84W x 36D x 32H inches", "Seats": "3 people", "Style": "Scandinavian"}'),
  ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'Industrial Bookshelf', 'Metal and wood bookshelf with industrial design', 349.00, 299.00, true, false, false, '*************-4444-4444-444444444444', 12, 'IB-005', '{"Material": "Metal frame, wood shelves", "Dimensions": "36W x 12D x 72H inches", "Shelves": "5 adjustable", "Style": "Industrial"}'),
  ('ffffffff-ffff-ffff-ffff-ffffffffffff', 'Pendant Light Fixture', 'Modern pendant light for kitchen or dining', 199.00, NULL, false, false, false, '55555555-5555-5555-5555-555555555555', 30, 'PLF-006', '{"Material": "Glass and metal", "Dimensions": "12 diameter x 18H inches", "Bulb": "E26 LED compatible", "Installation": "Hardwired"}')
ON CONFLICT (id) DO NOTHING;

-- Insert product images
INSERT INTO product_images (product_id, image_url, alt_text, is_primary, sort_order) VALUES
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80', 'Comfort Lounge Chair - Main View', true, 1),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80', 'Comfort Lounge Chair - Side View', false, 2),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'https://images.unsplash.com/photo-1549497538-303791108f95?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80', 'Modern Dining Table - Main View', true, 1),
  ('cccccccc-cccc-cccc-cccc-cccccccccccc', 'https://images.unsplash.com/photo-1505843490538-5133c6c7d0e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80', 'Executive Office Chair - Main View', true, 1),
  ('dddddddd-dddd-dddd-dddd-dddddddddddd', 'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80', 'Scandinavian Sofa - Main View', true, 1),
  ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80', 'Industrial Bookshelf - Main View', true, 1),
  ('ffffffff-ffff-ffff-ffff-ffffffffffff', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80', 'Pendant Light Fixture - Main View', true, 1)
ON CONFLICT DO NOTHING;

-- ============================================================================
-- STEP 4: VERIFICATION
-- ============================================================================

SELECT 'STEP 4: Verification' as step;

-- Check final counts
SELECT 
  (SELECT COUNT(*) FROM categories) as categories_created,
  (SELECT COUNT(*) FROM products) as products_created,
  (SELECT COUNT(*) FROM product_images) as images_created;

-- Show sample data
SELECT 'Sample products:' as info;
SELECT id, name, price, sale_price, is_sale, stock FROM products LIMIT 3;

SELECT 'Sample categories:' as info;
SELECT id, name FROM categories LIMIT 3;

SELECT 'SETUP COMPLETE! Your database now has sample products.' as result;
