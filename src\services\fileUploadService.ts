import { supabase } from '@/lib/supabase';
import { validateImageFile } from '@/utils/validation';

/**
 * Uploads a profile picture to Supabase Storage
 * @param userId The user ID
 * @param file The file to upload
 * @returns The URL of the uploaded file, or null if upload failed
 */
export const uploadProfilePicture = async (
  userId: string,
  file: File
): Promise<string | null> => {
  try {
    // Validate the file
    const validationError = validateImageFile(file);
    if (validationError) {
      throw new Error(validationError);
    }

    // Create a unique file name
    const fileExt = file.name.split('.').pop();
    const fileName = `${userId}/avatar-${Date.now()}.${fileExt}`;
    const filePath = `${fileName}`;

    // Upload the file to Supabase Storage
    const { data, error } = await supabase.storage
      .from('profile-pictures')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true
      });

    if (error) {
      throw error;
    }

    // Get the public URL
    const { data: { publicUrl } } = supabase.storage
      .from('profile-pictures')
      .getPublicUrl(filePath);

    return publicUrl;
  } catch (error) {
    console.error('Error uploading profile picture:', error);
    return null;
  }
};

/**
 * Deletes a profile picture from Supabase Storage
 * @param url The URL of the file to delete
 * @returns True if deletion was successful, false otherwise
 */
export const deleteProfilePicture = async (url: string): Promise<boolean> => {
  try {
    // Extract the file path from the URL
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/');
    const bucketName = pathParts[2]; // Assuming URL format is /storage/v1/object/public/bucket-name/file-path
    const filePath = pathParts.slice(3).join('/');

    // Delete the file
    const { error } = await supabase.storage
      .from(bucketName)
      .remove([filePath]);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error deleting profile picture:', error);
    return false;
  }
};
