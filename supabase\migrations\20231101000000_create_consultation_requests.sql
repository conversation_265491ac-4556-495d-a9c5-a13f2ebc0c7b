-- Create consultation_requests table
CREATE TABLE IF NOT EXISTS public.consultation_requests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  phone TEXT,
  project_type TEXT,
  message TEXT,
  status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  user_id UUID REFERENCES auth.users(id) NULL
);

-- Add comment to the table
COMMENT ON TABLE public.consultation_requests IS 'Stores consultation requests from the collections page form';

-- Create index on status for faster filtering
CREATE INDEX IF NOT EXISTS idx_consultation_requests_status ON public.consultation_requests(status);

-- Create index on created_at for sorting
CREATE INDEX IF NOT EXISTS idx_consultation_requests_created_at ON public.consultation_requests(created_at);

-- Set up Row Level Security (RLS)
ALTER TABLE public.consultation_requests ENABLE ROW LEVEL SECURITY;

-- Create RLS policies

-- 1. Allow anyone to insert (submit form)
CREATE POLICY "Anyone can submit a consultation request" 
ON public.consultation_requests FOR INSERT 
TO public
WITH CHECK (true);

-- 2. Allow authenticated users to view their own requests
CREATE POLICY "Users can view their own consultation requests" 
ON public.consultation_requests FOR SELECT 
TO authenticated
USING (user_id = auth.uid() OR user_id IS NULL);

-- 3. Allow admins to view all requests
CREATE POLICY "Admins can view all consultation requests" 
ON public.consultation_requests FOR SELECT 
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE user_profiles.id = auth.uid()
    AND user_profiles.role = 'admin'
  )
);

-- 4. Allow admins to update requests (change status)
CREATE POLICY "Admins can update consultation requests" 
ON public.consultation_requests FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM user_profiles
    WHERE user_profiles.id = auth.uid()
    AND user_profiles.role = 'admin'
  )
);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update the updated_at column
CREATE TRIGGER update_consultation_requests_updated_at
BEFORE UPDATE ON public.consultation_requests
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
