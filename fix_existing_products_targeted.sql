-- ============================================================================
-- TARGETED FIX FOR EXISTING PRODUCTS
-- This script will fix your existing products to work with the frontend
-- ============================================================================

SELECT 'TARGETED FIX FOR EXISTING PRODUCTS' as title;
SELECT '====================================' as separator;

-- ============================================================================
-- STEP 1: ANALYZE CURRENT PRODUCT STRUCTURE
-- ============================================================================

SELECT 'STEP 1: Analyzing current product structure' as step;

-- Show current products and their structure
SELECT 'Current products in database:' as info;
SELECT 
  id,
  name,
  price,
  CASE 
    WHEN sale_price IS NOT NULL AND sale_price > 0 THEN sale_price
    ELSE NULL
  END as sale_price,
  category_id,
  CASE 
    WHEN stock IS NOT NULL THEN stock
    WHEN stock_quantity IS NOT NULL THEN stock_quantity
    ELSE 0
  END as current_stock,
  status,
  created_at
FROM products 
ORDER BY created_at DESC;

-- Check what columns exist vs what frontend expects
SELECT 'Column analysis:' as info;
SELECT 
  column_name,
  data_type,
  CASE 
    WHEN column_name IN ('id', 'name', 'description', 'price', 'sale_price', 'category_id', 'created_at', 'updated_at') THEN '✅ REQUIRED'
    WHEN column_name IN ('is_sale', 'is_new', 'is_featured', 'status', 'stock', 'sku', 'specifications', 'rating', 'review_count', 'customization_available') THEN '🔧 FRONTEND EXPECTS'
    WHEN column_name IN ('stock_quantity', 'is_active', 'images') THEN '⚠️ OLD FORMAT'
    ELSE '❓ UNKNOWN'
  END as importance
FROM information_schema.columns 
WHERE table_name = 'products' 
ORDER BY 
  CASE 
    WHEN column_name IN ('id', 'name', 'description', 'price', 'sale_price', 'category_id', 'created_at', 'updated_at') THEN 1
    WHEN column_name IN ('is_sale', 'is_new', 'is_featured', 'status', 'stock', 'sku', 'specifications', 'rating', 'review_count', 'customization_available') THEN 2
    WHEN column_name IN ('stock_quantity', 'is_active', 'images') THEN 3
    ELSE 4
  END,
  column_name;

-- ============================================================================
-- STEP 2: ADD MISSING COLUMNS THAT FRONTEND EXPECTS
-- ============================================================================

SELECT 'STEP 2: Adding missing columns for frontend compatibility' as step;

-- Add columns that the frontend mapper expects
DO $$
BEGIN
  -- Add is_sale column
  IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'is_sale') THEN
    ALTER TABLE products ADD COLUMN is_sale BOOLEAN DEFAULT false;
    RAISE NOTICE 'Added is_sale column';
  END IF;
  
  -- Add is_new column
  IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'is_new') THEN
    ALTER TABLE products ADD COLUMN is_new BOOLEAN DEFAULT false;
    RAISE NOTICE 'Added is_new column';
  END IF;
  
  -- Add is_featured column
  IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'is_featured') THEN
    ALTER TABLE products ADD COLUMN is_featured BOOLEAN DEFAULT false;
    RAISE NOTICE 'Added is_featured column';
  END IF;
  
  -- Add status column
  IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'status') THEN
    ALTER TABLE products ADD COLUMN status TEXT CHECK (status IN ('active', 'draft', 'deleted')) DEFAULT 'active';
    RAISE NOTICE 'Added status column';
  END IF;
  
  -- Add stock column (frontend expects 'stock', not 'stock_quantity')
  IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'stock') THEN
    ALTER TABLE products ADD COLUMN stock INTEGER DEFAULT 0;
    RAISE NOTICE 'Added stock column';
  END IF;
  
  -- Add sku column
  IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'sku') THEN
    ALTER TABLE products ADD COLUMN sku TEXT;
    RAISE NOTICE 'Added sku column';
  END IF;
  
  -- Add specifications column
  IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'specifications') THEN
    ALTER TABLE products ADD COLUMN specifications JSONB DEFAULT '{}';
    RAISE NOTICE 'Added specifications column';
  END IF;
  
  -- Add rating column
  IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'rating') THEN
    ALTER TABLE products ADD COLUMN rating DECIMAL(3,2) DEFAULT 0;
    RAISE NOTICE 'Added rating column';
  END IF;
  
  -- Add review_count column
  IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'review_count') THEN
    ALTER TABLE products ADD COLUMN review_count INTEGER DEFAULT 0;
    RAISE NOTICE 'Added review_count column';
  END IF;
  
  -- Add customization_available column
  IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'customization_available') THEN
    ALTER TABLE products ADD COLUMN customization_available BOOLEAN DEFAULT false;
    RAISE NOTICE 'Added customization_available column';
  END IF;
END $$;

-- ============================================================================
-- STEP 3: MIGRATE DATA FROM OLD COLUMNS TO NEW COLUMNS
-- ============================================================================

SELECT 'STEP 3: Migrating data to frontend-compatible format' as step;

-- Copy stock_quantity to stock if stock is empty
UPDATE products 
SET stock = stock_quantity 
WHERE stock = 0 AND stock_quantity IS NOT NULL AND stock_quantity > 0;

-- Set default stock for products with no stock
UPDATE products 
SET stock = 10 
WHERE stock = 0 OR stock IS NULL;

-- Set is_sale based on sale_price
UPDATE products 
SET is_sale = CASE 
  WHEN sale_price IS NOT NULL AND sale_price > 0 AND sale_price < price THEN true
  ELSE false
END;

-- Set all products to active status
UPDATE products 
SET status = 'active' 
WHERE status IS NULL OR status = '';

-- Generate SKUs for products without them
UPDATE products 
SET sku = 'SKU-' || SUBSTRING(id::text, 1, 8)
WHERE sku IS NULL OR sku = '';

-- Set some products as featured (first 3 products)
UPDATE products 
SET is_featured = true 
WHERE id IN (
  SELECT id FROM products 
  ORDER BY created_at ASC 
  LIMIT 3
);

-- Set some products as new (most recent 2 products)
UPDATE products 
SET is_new = true 
WHERE id IN (
  SELECT id FROM products 
  ORDER BY created_at DESC 
  LIMIT 2
);

-- ============================================================================
-- STEP 4: ENSURE CATEGORIES TABLE EXISTS AND IS POPULATED
-- ============================================================================

SELECT 'STEP 4: Ensuring categories are properly set up' as step;

-- Create categories table if it doesn't exist
CREATE TABLE IF NOT EXISTS categories (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  image_url TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create default categories
INSERT INTO categories (id, name, description, is_active) VALUES
  ('11111111-1111-1111-1111-111111111111', 'Furniture', 'General furniture items', true),
  ('*************-2222-2222-************', 'Chairs', 'Seating solutions', true),
  ('*************-3333-3333-************', 'Tables', 'Dining and work tables', true),
  ('*************-4444-4444-************', 'Storage', 'Storage solutions', true),
  ('*************-5555-5555-************', 'Lighting', 'Lighting fixtures', true),
  ('*************-6666-6666-************', 'Decor', 'Decorative items', true)
ON CONFLICT (name) DO NOTHING;

-- Assign products to default category if they don't have a valid one
DO $$
DECLARE
  default_category_id UUID;
BEGIN
  SELECT id INTO default_category_id FROM categories LIMIT 1;
  
  IF default_category_id IS NOT NULL THEN
    UPDATE products 
    SET category_id = default_category_id 
    WHERE category_id IS NULL 
       OR NOT EXISTS (SELECT 1 FROM categories WHERE id = products.category_id);
    
    RAISE NOTICE 'Assigned orphaned products to default category';
  END IF;
END $$;

-- ============================================================================
-- STEP 5: ENSURE PRODUCT_IMAGES TABLE EXISTS AND IS POPULATED
-- ============================================================================

SELECT 'STEP 5: Setting up product images' as step;

-- Create product_images table if it doesn't exist
CREATE TABLE IF NOT EXISTS product_images (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  image_url TEXT NOT NULL,
  alt_text TEXT,
  is_primary BOOLEAN DEFAULT false,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- If products have an 'images' JSONB column, migrate to product_images table
DO $$
DECLARE
  product_record RECORD;
  image_url TEXT;
  image_index INTEGER;
BEGIN
  IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'images') THEN
    FOR product_record IN SELECT id, images FROM products WHERE images IS NOT NULL AND jsonb_array_length(images) > 0 LOOP
      image_index := 0;
      FOR image_url IN SELECT jsonb_array_elements_text(product_record.images) LOOP
        INSERT INTO product_images (product_id, image_url, alt_text, is_primary, sort_order)
        VALUES (
          product_record.id,
          image_url,
          'Product image',
          image_index = 0,
          image_index
        )
        ON CONFLICT DO NOTHING;
        image_index := image_index + 1;
      END LOOP;
    END LOOP;
    RAISE NOTICE 'Migrated images from JSONB to product_images table';
  END IF;
END $$;

-- Add default images for products without any
INSERT INTO product_images (product_id, image_url, alt_text, is_primary, sort_order)
SELECT 
  p.id,
  'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
  p.name || ' - Product Image',
  true,
  0
FROM products p
WHERE NOT EXISTS (SELECT 1 FROM product_images WHERE product_id = p.id)
ON CONFLICT DO NOTHING;

-- ============================================================================
-- STEP 6: FINAL VERIFICATION AND TESTING
-- ============================================================================

SELECT 'STEP 6: Final verification' as step;

-- Test the exact query that the frontend uses
SELECT 'Testing frontend query:' as test;
SELECT 
  p.id,
  p.name,
  p.price,
  p.sale_price,
  p.is_sale,
  p.is_new,
  p.is_featured,
  p.status,
  p.stock,
  c.name as category_name,
  pi.image_url as primary_image
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = true
WHERE p.status = 'active'
ORDER BY p.created_at DESC
LIMIT 5;

-- Show summary
SELECT 'SUMMARY:' as summary;
SELECT 
  'Total products' as metric,
  COUNT(*) as value
FROM products
UNION ALL
SELECT 
  'Active products' as metric,
  COUNT(*) as value
FROM products WHERE status = 'active'
UNION ALL
SELECT 
  'Products with images' as metric,
  COUNT(DISTINCT product_id) as value
FROM product_images
UNION ALL
SELECT 
  'Products on sale' as metric,
  COUNT(*) as value
FROM products WHERE is_sale = true
UNION ALL
SELECT 
  'Featured products' as metric,
  COUNT(*) as value
FROM products WHERE is_featured = true;

SELECT 'EXISTING PRODUCTS HAVE BEEN FIXED!' as result;
SELECT 'Your products should now be visible on the website.' as message;
SELECT 'Clear your browser cache and refresh the page.' as instruction;
