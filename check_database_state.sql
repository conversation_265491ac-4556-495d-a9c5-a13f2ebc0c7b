-- Check Current Database State
-- Run this first to understand what we're working with

-- 1. Check if orders table exists and its structure
SELECT 
  column_name, 
  data_type, 
  is_nullable, 
  column_default
FROM information_schema.columns 
WHERE table_name = 'orders' 
ORDER BY ordinal_position;

-- 2. Check current payment methods in orders (this might show the problematic data)
SELECT 
  payment_method, 
  COUNT(*) as count 
FROM orders 
GROUP BY payment_method 
ORDER BY count DESC;

-- 3. Check if payment_transactions table exists
SELECT EXISTS (
  SELECT 1 
  FROM information_schema.tables 
  WHERE table_name = 'payment_transactions'
) as payment_transactions_exists;

-- 4. Check existing constraints on orders table
SELECT 
  conname as constraint_name,
  pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'orders'::regclass
AND contype = 'c'; -- Check constraints only

-- 5. Check if we have any orders with problematic payment methods
SELECT 
  id,
  payment_method,
  status,
  created_at
FROM orders 
WHERE payment_method NOT IN ('cash_on_delivery', 'online_payment', 'razorpay', 'upi', 'card', 'netbanking', 'wallet')
OR payment_method IS NULL
LIMIT 10;
