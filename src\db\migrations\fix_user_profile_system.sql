-- Comprehensive fix for user profile system
-- This script fixes issues with user profiles, RLS policies, and triggers

-- First, check if the user_profiles table exists and create it if not
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_name = 'user_profiles'
  ) THEN
    CREATE TABLE user_profiles (
      id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
      display_name TEXT,
      email TEXT,
      role TEXT CHECK (role IN ('user', 'admin')) DEFAULT 'user',
      phone TEXT,
      dob DATE,
      street TEXT,
      city TEXT,
      state TEXT,
      postal_code TEXT,
      country TEXT,
      avatar_url TEXT,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    RAISE NOTICE 'Created user_profiles table';
  ELSE
    -- Make sure all required columns exist
    IF NOT EXISTS (
      SELECT 1
      FROM information_schema.columns
      WHERE table_name = 'user_profiles'
      AND column_name = 'phone'
    ) THEN
      ALTER TABLE user_profiles ADD COLUMN phone TEXT;
    END IF;
    
    IF NOT EXISTS (
      SELECT 1
      FROM information_schema.columns
      WHERE table_name = 'user_profiles'
      AND column_name = 'dob'
    ) THEN
      ALTER TABLE user_profiles ADD COLUMN dob DATE;
    END IF;
    
    IF NOT EXISTS (
      SELECT 1
      FROM information_schema.columns
      WHERE table_name = 'user_profiles'
      AND column_name = 'street'
    ) THEN
      ALTER TABLE user_profiles ADD COLUMN street TEXT;
    END IF;
    
    IF NOT EXISTS (
      SELECT 1
      FROM information_schema.columns
      WHERE table_name = 'user_profiles'
      AND column_name = 'city'
    ) THEN
      ALTER TABLE user_profiles ADD COLUMN city TEXT;
    END IF;
    
    IF NOT EXISTS (
      SELECT 1
      FROM information_schema.columns
      WHERE table_name = 'user_profiles'
      AND column_name = 'state'
    ) THEN
      ALTER TABLE user_profiles ADD COLUMN state TEXT;
    END IF;
    
    IF NOT EXISTS (
      SELECT 1
      FROM information_schema.columns
      WHERE table_name = 'user_profiles'
      AND column_name = 'postal_code'
    ) THEN
      ALTER TABLE user_profiles ADD COLUMN postal_code TEXT;
    END IF;
    
    IF NOT EXISTS (
      SELECT 1
      FROM information_schema.columns
      WHERE table_name = 'user_profiles'
      AND column_name = 'country'
    ) THEN
      ALTER TABLE user_profiles ADD COLUMN country TEXT;
    END IF;
    
    IF NOT EXISTS (
      SELECT 1
      FROM information_schema.columns
      WHERE table_name = 'user_profiles'
      AND column_name = 'avatar_url'
    ) THEN
      ALTER TABLE user_profiles ADD COLUMN avatar_url TEXT;
    END IF;
    
    RAISE NOTICE 'Updated user_profiles table structure';
  END IF;
END $$;

-- Enable RLS on the user_profiles table
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Drop all existing policies to start fresh
DROP POLICY IF EXISTS "Users can view their own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON user_profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Admins can update all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Admins can insert profiles" ON user_profiles;
DROP POLICY IF EXISTS "Admins can delete profiles" ON user_profiles;
DROP POLICY IF EXISTS "Allow anonymous count of user_profiles" ON user_profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON user_profiles;

-- Create a function to check if a user is an admin
-- This avoids recursion by directly checking the role in the table
CREATE OR REPLACE FUNCTION is_admin(user_id uuid)
RETURNS BOOLEAN AS $$
DECLARE
  user_role TEXT;
BEGIN
  SELECT role INTO user_role FROM user_profiles WHERE id = user_id;
  RETURN user_role = 'admin';
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create new, simplified policies

-- 1. Allow users to view their own profile
CREATE POLICY "Users can view their own profile"
  ON user_profiles FOR SELECT
  USING (auth.uid() = id);

-- 2. Allow users to update their own profile
CREATE POLICY "Users can update their own profile"
  ON user_profiles FOR UPDATE
  USING (auth.uid() = id);

-- 3. Allow users to insert their own profile (for manual creation)
CREATE POLICY "Users can insert their own profile"
  ON user_profiles FOR INSERT
  WITH CHECK (auth.uid() = id);

-- 4. Allow admins to view all profiles
CREATE POLICY "Admins can view all profiles"
  ON user_profiles FOR SELECT
  USING (is_admin(auth.uid()));

-- 5. Allow admins to update all profiles
CREATE POLICY "Admins can update all profiles"
  ON user_profiles FOR UPDATE
  USING (is_admin(auth.uid()));

-- 6. Allow admins to insert profiles
CREATE POLICY "Admins can insert profiles"
  ON user_profiles FOR INSERT
  WITH CHECK (is_admin(auth.uid()));

-- 7. Allow admins to delete profiles
CREATE POLICY "Admins can delete profiles"
  ON user_profiles FOR DELETE
  USING (is_admin(auth.uid()));

-- Fix the handle_new_user function to be more robust
CREATE OR REPLACE FUNCTION public.handle_new_user() 
RETURNS TRIGGER AS $$
BEGIN
  -- Check if a profile already exists for this user
  -- This prevents duplicate key errors
  IF EXISTS (
    SELECT 1 FROM public.user_profiles WHERE id = new.id
  ) THEN
    -- Profile already exists, just update it
    UPDATE public.user_profiles
    SET 
      display_name = COALESCE(new.raw_user_meta_data->>'name', split_part(new.email, '@', 1)),
      email = new.email,
      updated_at = now()
    WHERE id = new.id;
  ELSE
    -- Insert new profile
    INSERT INTO public.user_profiles (
      id, 
      display_name, 
      email, 
      role,
      created_at,
      updated_at
    )
    VALUES (
      new.id, 
      COALESCE(new.raw_user_meta_data->>'name', split_part(new.email, '@', 1)),
      new.email,
      'user',  -- Default role is always 'user'
      now(),
      now()
    );
  END IF;
  
  RETURN new;
EXCEPTION
  WHEN others THEN
    -- Log the error but don't prevent user creation
    RAISE WARNING 'Error in handle_new_user trigger: %', SQLERRM;
    RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Make sure the trigger is set up
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Create profiles for existing users who don't have one
INSERT INTO user_profiles (id, display_name, email, role, created_at, updated_at)
SELECT 
  id,
  COALESCE(raw_user_meta_data->>'name', split_part(email, '@', 1)),
  email,
  'user',
  now(),
  now()
FROM auth.users
WHERE NOT EXISTS (
  SELECT 1 FROM user_profiles WHERE user_profiles.id = auth.users.id
)
ON CONFLICT (id) DO NOTHING;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO postgres, anon, authenticated, service_role;
