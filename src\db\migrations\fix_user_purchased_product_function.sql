-- Fix the has_user_purchased_product function to resolve the ambiguous user_id column issue

-- Drop the existing function if it exists
DROP FUNCTION IF EXISTS has_user_purchased_product(UUID, UUID);

-- Create a new version of the function with explicit column references
CREATE OR REPLACE FUNCTION has_user_purchased_product(input_user_id UUID, input_product_id UUID)
RET<PERSON>NS BOOLEAN AS $$
DECLARE
  has_purchased BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM orders o
    JOIN order_items oi ON o.id = oi.order_id
    WHERE o.user_id = input_user_id
    AND oi.product_id = input_product_id
    AND o.status IN ('delivered', 'shipped') -- Only count delivered or shipped orders
  ) INTO has_purchased;
  
  RETURN has_purchased;
END;
$$ LANGUAGE plpgsql;

-- Add comment to explain the function
COMMENT ON FUNCTION has_user_purchased_product IS 'Checks if a user has purchased a specific product';
