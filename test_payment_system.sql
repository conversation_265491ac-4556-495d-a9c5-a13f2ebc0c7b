-- Test Payment System
-- This script tests the payment system functionality

-- 1. Check current orders table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'orders' 
ORDER BY ordinal_position;

-- 2. Check payment_transactions table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'payment_transactions' 
ORDER BY ordinal_position;

-- 3. Check current payment methods in orders
SELECT 
  payment_method, 
  payment_status,
  COUNT(*) as count 
FROM orders 
GROUP BY payment_method, payment_status 
ORDER BY payment_method, payment_status;

-- 4. Check constraints on orders table
SELECT 
  conname as constraint_name,
  contype as constraint_type,
  pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'orders'::regclass;

-- 5. Test inserting a new order with proper payment method
INSERT INTO orders (
  user_id, 
  status, 
  payment_method, 
  payment_status,
  total_amount, 
  shipping_address
) VALUES (
  (SELECT id FROM auth.users LIMIT 1),
  'pending',
  'cash_on_delivery',
  'pending',
  100.00,
  '{"name": "Test User", "street": "123 Test St", "city": "Test City", "state": "Test State", "postal_code": "12345", "country": "India"}'::jsonb
) RETURNING id, payment_method, payment_status;

-- 6. Check if the insert was successful
SELECT 
  id,
  payment_method,
  payment_status,
  total_amount,
  created_at
FROM orders 
ORDER BY created_at DESC 
LIMIT 5;
