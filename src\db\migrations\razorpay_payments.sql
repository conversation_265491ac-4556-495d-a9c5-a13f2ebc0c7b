-- Razorpay Payments Table Migration
-- This migration creates the razorpay_payments table and related functions

-- Create razorpay_payments table
CREATE TABLE IF NOT EXISTS razorpay_payments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  razorpay_order_id TEXT NOT NULL,
  razorpay_payment_id TEXT,
  amount DECIMAL(10, 2) NOT NULL,
  currency TEXT NOT NULL DEFAULT 'INR',
  status TEXT NOT NULL CHECK (status IN ('created', 'authorized', 'captured', 'failed', 'refunded')),
  method TEXT,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  error_description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_razorpay_payments_order_id ON razorpay_payments(order_id);
CREATE INDEX IF NOT EXISTS idx_razorpay_payments_razorpay_order_id ON razorpay_payments(razorpay_order_id);
CREATE INDEX IF NOT EXISTS idx_razorpay_payments_user_id ON razorpay_payments(user_id);
CREATE INDEX IF NOT EXISTS idx_razorpay_payments_status ON razorpay_payments(status);

-- Add Row Level Security (RLS) policies
ALTER TABLE razorpay_payments ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their own payments
CREATE POLICY user_select_own_payments ON razorpay_payments
  FOR SELECT
  USING (auth.uid() = user_id);

-- Policy for users to insert their own payments
CREATE POLICY user_insert_own_payments ON razorpay_payments
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Policy for admins to view all payments
CREATE POLICY admin_select_all_payments ON razorpay_payments
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE user_profiles.id = auth.uid()
      AND user_profiles.role = 'admin'
    )
  );

-- Policy for admins to update all payments
CREATE POLICY admin_update_all_payments ON razorpay_payments
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE user_profiles.id = auth.uid()
      AND user_profiles.role = 'admin'
    )
  );

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_razorpay_payments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update updated_at timestamp
CREATE TRIGGER update_razorpay_payments_updated_at
BEFORE UPDATE ON razorpay_payments
FOR EACH ROW
EXECUTE FUNCTION update_razorpay_payments_updated_at();

-- Add payment_status, razorpay_order_id, and razorpay_payment_id columns to orders table if they don't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'orders' AND column_name = 'payment_status') THEN
    ALTER TABLE orders ADD COLUMN payment_status TEXT CHECK (payment_status IN ('pending', 'paid', 'failed'));
  END IF;

  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'orders' AND column_name = 'razorpay_order_id') THEN
    ALTER TABLE orders ADD COLUMN razorpay_order_id TEXT;
  END IF;

  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'orders' AND column_name = 'razorpay_payment_id') THEN
    ALTER TABLE orders ADD COLUMN razorpay_payment_id TEXT;
  END IF;
END $$;
