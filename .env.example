# ===========================================
# THE BADHEES - ENVIRONMENT CONFIGURATION
# ===========================================
# Copy this file to .env and fill in your actual values
# Add .env to .gitignore to keep credentials secure

# ===========================================
# SUPABASE CONFIGURATION
# ===========================================
# Get these from your Supabase project dashboard (https://supabase.com/dashboard)
# 1. Go to Settings > API
# 2. Copy Project URL and anon/public key

VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# ===========================================
# RAZORPAY CONFIGURATION
# ===========================================
# Get these from your Razorpay dashboard (https://dashboard.razorpay.com/)
# 1. Go to Settings > API Keys
# 2. Generate Test/Live keys

# Frontend key (public - safe to expose in browser)
VITE_RAZORPAY_KEY_ID=rzp_test_your_key_id_here

# Backend keys (private - for API routes only)
RAZORPAY_KEY_ID=rzp_test_your_key_id_here
RAZORPAY_SECRET=your_razorpay_secret_key_here
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret_here

# ===========================================
# DEVELOPMENT SETTINGS
# ===========================================
# Server port for local development
VITE_SERVER_PORT=3001

# Enable debug mode (optional)
VITE_DEBUG_MODE=true

# ===========================================
# PRODUCTION DEPLOYMENT NOTES
# ===========================================
# For production deployment:
# 1. Use rzp_live_ keys instead of rzp_test_
# 2. Set VITE_DEBUG_MODE=false
# 3. Set these environment variables in your hosting platform
# 4. NEVER put production keys in this file

# ===========================================
# EMAIL CONFIGURATION (Optional)
# ===========================================
# For transactional emails via Supabase Edge Functions
EMAIL_SERVICE=supabase
EMAIL_FROM=<EMAIL>

# Alternative email services (uncomment to use)
# EMAIL_SERVICE=sendgrid
# EMAIL_API_KEY=your_sendgrid_api_key
# EMAIL_FROM=<EMAIL>

# ===========================================
# ADDITIONAL SETTINGS
# ===========================================
# App URL for redirects and webhooks
VITE_APP_URL=http://localhost:8080

# Enable analytics (optional)
VITE_ENABLE_ANALYTICS=false
