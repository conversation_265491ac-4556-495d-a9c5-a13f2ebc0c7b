-- Fix the foreign key relationship between orders.user_id and auth.users.id

-- First, check if the orders table exists
DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'orders'
  ) THEN
    -- Drop the existing foreign key constraint if it exists
    ALTER TABLE IF EXISTS public.orders 
    DROP CONSTRAINT IF EXISTS orders_user_id_fkey;
    
    -- Add the correct foreign key constraint
    ALTER TABLE public.orders
    ADD CONSTRAINT orders_user_id_fkey
    FOREIGN KEY (user_id)
    REFERENCES auth.users(id)
    ON DELETE CASCADE;
    
    RAISE NOTICE 'Fixed foreign key relationship between orders.user_id and auth.users.id';
  ELSE
    RAISE NOTICE 'Orders table does not exist';
  END IF;
END $$;
