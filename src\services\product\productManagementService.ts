/**
 * Product Management Service
 * 
 * This module provides functions to create, update, and delete products.
 */
import { supabase } from '@/lib/supabase';
import { handleSupabaseError, showSuccessToast } from '@/utils/supabaseHelpers';
import { FrontendProduct, ProductInput } from './types';
import { getProductById } from './productService';

/**
 * Create a new product
 * @param product Product data
 * @returns Created product or null if creation failed
 */
export const createProduct = async (product: ProductInput): Promise<FrontendProduct | null> => {
  try {
    // First, find the category ID
    const { data: categoryData, error: categoryError } = await supabase
      .from('categories')
      .select('id')
      .eq('name', product.category)
      .single();

    if (categoryError) {
      throw new Error(`Category not found: ${product.category}`);
    }

    // Insert the product
    const { data, error } = await supabase
      .from('products')
      .insert({
        name: product.name,
        description: product.description || '',
        price: product.price,
        sale_price: product.salePrice,
        is_sale: product.isSale || false,
        is_new: product.isNew || false,
        is_featured: product.isFeatured || false,
        category_id: categoryData.id,
        status: product.status,
        stock: product.stock,
        sku: product.sku || '',
        customization_available: product.customizationAvailable || false,
        specifications: product.specifications || {},
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Insert primary image
    if (product.image) {
      const { error: imageError } = await supabase
        .from('product_images')
        .insert({
          product_id: data.id,
          image_url: product.image,
          is_primary: true,
          display_order: 0,
        });

      if (imageError) {
        console.error('Error inserting primary image:', imageError);
      }
    }

    // Insert additional images
    if (product.images && product.images.length > 0) {
      const additionalImages = product.images
        .filter((img) => img && img !== product.image) // Skip empty URLs and the primary image if it's duplicated
        .map((imageUrl, index) => ({
          product_id: data.id,
          image_url: imageUrl,
          is_primary: false,
          display_order: index + 1,
        }));

      if (additionalImages.length > 0) {
        const { error: imagesError } = await supabase
          .from('product_images')
          .insert(additionalImages);

        if (imagesError) {
          console.error('Error inserting additional images:', imagesError);
        }
      }
    }

    showSuccessToast('Product created', `${product.name} has been created successfully`);

    return getProductById(data.id);
  } catch (error) {
    handleSupabaseError(error, 'createProduct');
    return null;
  }
};

/**
 * Update an existing product
 * @param product Product data with ID
 * @returns Updated product or null if update failed
 */
export const updateProduct = async (product: ProductInput): Promise<FrontendProduct | null> => {
  try {
    if (!product.id) {
      throw new Error('Product ID is required for update');
    }
    
    // First, find the category ID
    const { data: categoryData, error: categoryError } = await supabase
      .from('categories')
      .select('id')
      .eq('name', product.category)
      .single();

    if (categoryError) {
      throw new Error(`Category not found: ${product.category}`);
    }

    // Update the product
    const { error } = await supabase
      .from('products')
      .update({
        name: product.name,
        description: product.description || '',
        price: product.price,
        sale_price: product.salePrice,
        is_sale: product.isSale || false,
        is_new: product.isNew || false,
        is_featured: product.isFeatured || false,
        category_id: categoryData.id,
        status: product.status,
        stock: product.stock,
        sku: product.sku || '',
        customization_available: product.customizationAvailable || false,
        specifications: product.specifications || {},
        updated_at: new Date().toISOString(),
      })
      .eq('id', product.id);

    if (error) {
      throw error;
    }

    // Delete existing images
    const { error: deleteImagesError } = await supabase
      .from('product_images')
      .delete()
      .eq('product_id', product.id);

    if (deleteImagesError) {
      console.error('Error deleting existing images:', deleteImagesError);
    }

    // Insert primary image
    if (product.image) {
      const { error: imageError } = await supabase
        .from('product_images')
        .insert({
          product_id: product.id,
          image_url: product.image,
          is_primary: true,
          display_order: 0,
        });

      if (imageError) {
        console.error('Error inserting primary image:', imageError);
      }
    }

    // Insert additional images
    if (product.images && product.images.length > 0) {
      const additionalImages = product.images
        .filter((img) => img && img !== product.image) // Skip empty URLs and the primary image if it's duplicated
        .map((imageUrl, index) => ({
          product_id: product.id,
          image_url: imageUrl,
          is_primary: false,
          display_order: index + 1,
        }));

      if (additionalImages.length > 0) {
        const { error: imagesError } = await supabase
          .from('product_images')
          .insert(additionalImages);

        if (imagesError) {
          console.error('Error inserting additional images:', imagesError);
        }
      }
    }

    showSuccessToast('Product updated', `${product.name} has been updated successfully`);

    return getProductById(product.id);
  } catch (error) {
    handleSupabaseError(error, 'updateProduct');
    return null;
  }
};

/**
 * Delete a product
 * @param id Product ID
 * @returns Success status
 */
export const deleteProduct = async (id: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    showSuccessToast('Product deleted', 'The product has been deleted successfully');

    return true;
  } catch (error) {
    handleSupabaseError(error, 'deleteProduct');
    return false;
  }
};
