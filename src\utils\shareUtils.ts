/**
 * Utility functions for sharing content
 */

/**
 * Share a product using the Web Share API if available,
 * or fallback to copying the URL to clipboard
 * 
 * @param product The product to share
 * @param baseUrl The base URL of the site (optional, defaults to current origin)
 * @returns Promise that resolves to true if sharing was successful
 */
export const shareProduct = async (
  product: { id: string; name: string; description?: string; image?: string },
  baseUrl?: string
): Promise<boolean> => {
  try {
    // Construct the product URL
    const url = `${baseUrl || window.location.origin}/products/${product.id}`;
    
    // Prepare share data
    const shareData = {
      title: product.name,
      text: product.description || `Check out this product: ${product.name}`,
      url: url,
    };

    // Check if Web Share API is available
    if (navigator.share && navigator.canShare && navigator.canShare(shareData)) {
      // Use Web Share API
      await navigator.share(shareData);
      console.log('Product shared successfully');
      return true;
    } else {
      // Fallback: Copy URL to clipboard
      await navigator.clipboard.writeText(url);
      console.log('Product URL copied to clipboard');
      
      // Return true to indicate success even though we used fallback
      return true;
    }
  } catch (error) {
    console.error('Error sharing product:', error);
    return false;
  }
};

/**
 * Check if the Web Share API is available in the current browser
 * 
 * @returns Boolean indicating if sharing is supported
 */
export const isShareSupported = (): boolean => {
  return typeof navigator !== 'undefined' && !!navigator.share;
};

/**
 * Check if the Clipboard API is available for the fallback option
 * 
 * @returns Boolean indicating if clipboard copying is supported
 */
export const isClipboardSupported = (): boolean => {
  return typeof navigator !== 'undefined' && !!navigator.clipboard;
};
