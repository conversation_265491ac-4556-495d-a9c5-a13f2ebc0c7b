
import { useState, useEffect } from "react";
import "../styles/ProductDetail.css";
import { use<PERSON>ara<PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import { supabase } from "@/lib/supabase";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Minus, Plus, ChevronRight, Check, Star, Truck, RefreshCw, Shield, ZoomIn, ZoomOut, X, Loader2, Scissors, ShoppingCart } from "lucide-react";
import { cn } from "@/lib/utils";
import ProductCard from "@/components/products/ProductCard";
import { useCart } from "@/context/SupabaseCartContext";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import CustomizationRequestForm from "@/components/products/CustomizationRequestForm";
import { useAuth } from "@/context/SupabaseAuthContext";
import { ProductReview } from "@/services/productReviewsService";
import { Button } from "@/components/ui/button-system";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import EnhancedProductGallery from "@/components/products/EnhancedProductGallery";
import { useProduct, useProducts } from "@/hooks/useProducts";
import { useProductReviews, useHasPurchasedProduct, useAddProductReview } from "@/hooks/useProductDetail";
import { useProductRating } from "@/hooks/useProductRating";
import { toast } from "@/hooks/use-toast";

const ProductDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [activeTab, setActiveTab] = useState<"description" | "specifications" | "reviews">("description");
  const [activeImage, setActiveImage] = useState(0);
  const [imageOpen, setImageOpen] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [customizationFormOpen, setCustomizationFormOpen] = useState(false);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [reviewFormData, setReviewFormData] = useState({ rating: 5, title: '', comment: '' });

  const { addToCart } = useCart();
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();

  // Fetch product data using React Query
  const { data: product, isLoading } = useProduct(id || '');

  // Get real-time rating data
  const { rating: realRating, reviewCount: realReviewCount, isLoading: ratingLoading } = useProductRating(id || '');

  // Fetch all products for recommendations
  const { data: allProducts = [] } = useProducts();

  // Fetch product reviews
  const {
    data: reviews = [],
    isLoading: isLoadingReviews
  } = useProductReviews(id || '');

  // Check if user has purchased this product
  const {
    data: hasPurchased = false,
    isLoading: isCheckingPurchase
  } = useHasPurchasedProduct(id || '');

  // Mutation for adding a review
  const {
    mutate: submitReview,
    isPending: isSubmittingReview
  } = useAddProductReview();

  useEffect(() => {
    window.scrollTo(0, 0);

    // Reset active image when product changes
    setActiveImage(0);
  }, [id]);

  // Handle review submission
  const handleReviewSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isAuthenticated) {
      navigate('/login?redirect=' + encodeURIComponent(window.location.pathname));
      return;
    }

    const { rating, title, comment } = reviewFormData;

    submitReview(
      {
        productId: id!,
        rating,
        comment,
        title
      },
      {
        onSuccess: () => {
          // Reset form
          setReviewFormData({ rating: 5, title: '', comment: '' });
          setShowReviewForm(false);

          // Show success message
          toast({
            title: 'Review submitted',
            description: 'Thank you for your feedback!'
          });
        },
        onError: (error) => {
          console.error('Error submitting review:', error);

          // Show error message
          toast({
            title: 'Error submitting review',
            description: 'There was a problem submitting your review. Please try again.',
            variant: 'destructive'
          });
        }
      }
    );
  };

  // Add proper fallback for loading state and product not found
  if (isLoading) {
    return (
      <div className="min-h-screen">
        <Navbar />
        <div className="section-container pt-28 pb-16 flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="h-10 w-10 animate-spin text-badhees-accent mx-auto mb-4" />
            <p className="text-badhees-600">Loading product details...</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen">
        <Navbar />
        <div className="section-container pt-28 pb-16 flex items-center justify-center">
          <div className="text-center max-w-md">
            <h1 className="text-2xl font-bold text-badhees-800 mb-4">Product Not Found</h1>
            <p className="text-badhees-600 mb-6">The product you're looking for doesn't exist or has been removed.</p>
            <div className="flex justify-center gap-4">
              <Link to="/products" className="px-4 py-2 bg-badhees-accent text-white rounded-md hover:bg-badhees-700 transition-colors">
                Browse Products
              </Link>
              <Link to="/" className="px-4 py-2 border border-badhees-200 text-badhees-600 rounded-md hover:bg-badhees-50 transition-colors">
                Go Home
              </Link>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  // Get similar products from the same category
  const similarProducts = allProducts
    .filter(p => p.category === product.category && p.id !== product.id)
    .slice(0, 4);

  // Get complementary products (different category, similar price range)
  const complementaryProducts = allProducts
    .filter(p =>
      p.category !== product.category &&
      p.id !== product.id &&
      p.price >= product.price * 0.7 &&
      p.price <= product.price * 1.3
    )
    .slice(0, 4);

  // Get frequently bought together (random selection for demo)
  const frequentlyBoughtTogether = allProducts
    .filter(p => p.id !== product.id)
    .sort(() => 0.5 - Math.random())
    .slice(0, 3);

  const handleAddToCart = () => {
    addToCart(product, 1);
  };

  const handleBuyNow = () => {
    addToCart(product, 1);
    navigate('/cart');
  };

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 0.5, 3));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 0.5, 1));
  };

  const handleImageClick = () => {
    setImageOpen(true);
  };

  const handleCloseZoom = () => {
    setImageOpen(false);
    setZoomLevel(1);
  };

  // Use product images if available, otherwise use the main image
  const productImages = product.images && product.images.length > 0
    ? product.images
    : [product.image];

  return (
    <div className="min-h-screen">
      <Navbar />

      <div className="pt-20 sm:pt-28 pb-8 sm:pb-12">
        {/* Breadcrumbs - Hidden on mobile for space efficiency */}
        <div className="max-w-[1400px] mx-auto px-4 sm:px-8 mb-4 sm:mb-8 hidden sm:block">
          <div className="flex items-center text-sm text-badhees-500">
            <Link to="/" className="hover:text-badhees-accent">Home</Link>
            <ChevronRight className="h-3 w-3 mx-2" />
            <Link to="/products" className="hover:text-badhees-accent">Products</Link>
            <ChevronRight className="h-3 w-3 mx-2" />
            <Link to={`/products?category=${product.category}`} className="hover:text-badhees-accent">{product.category}</Link>
            <ChevronRight className="h-3 w-3 mx-2" />
            <span className="text-badhees-800 font-medium">{product.name}</span>
          </div>
        </div>

        <div className="max-w-[1400px] mx-auto px-4 sm:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-12">
            {/* Product Images */}
            <EnhancedProductGallery
              images={productImages}
              productName={product.name}
              enable360View={product.has360View}
              view360Images={product.view360Images || []}
            />

            {/* Product Info */}
            <div className="space-y-3 sm:space-y-6">
              <div>
                <div className="text-sm text-badhees-500 mb-1 sm:mb-2">{product.category}</div>
                <h1 className="text-2xl sm:text-3xl font-bold text-badhees-800">{product.name}</h1>

                <div className="flex items-center mt-1 sm:mt-2 space-x-2 sm:space-x-4">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={cn(
                          "h-3 w-3 sm:h-4 sm:w-4",
                          i < Math.round(realRating || product.rating || 0) ? "text-yellow-400 fill-yellow-400" : "text-badhees-200"
                        )}
                      />
                    ))}
                    <span className="ml-1 sm:ml-2 text-xs sm:text-sm text-badhees-600">
                      {(realRating || product.rating || 0).toFixed(1)} ({realReviewCount || product.reviewCount || 0})
                    </span>
                    {ratingLoading && (
                      <span className="ml-2 text-xs text-badhees-400">Updating...</span>
                    )}
                  </div>
                </div>
              </div>

              <div className="pt-2 sm:pt-4">
                {product.isSale && product.salePrice ? (
                  <div className="flex items-center space-x-2 sm:space-x-3">
                    <span className="text-xl sm:text-2xl font-bold text-badhees-accent">₹{product.salePrice.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
                    <span className="text-sm sm:text-lg text-badhees-400 line-through">₹{product.price.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
                    <span className="px-2 py-0.5 sm:py-1 text-xs font-medium bg-red-100 text-red-600 rounded">
                      {Math.round(((product.price - product.salePrice) / product.price) * 100)}% OFF
                    </span>
                  </div>
                ) : (
                  <span className="text-xl sm:text-2xl font-bold text-badhees-800">₹{product.price.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
                )}
              </div>

              <div className="pt-2 sm:pt-4">
                {/* Mobile-optimized layout */}
                <div className="flex flex-col space-y-2 sm:space-y-3">
                  {/* Action buttons */}
                  <div className="grid grid-cols-2 gap-2 sm:gap-3">
                    <Button
                      type="button"
                      variant="primary"
                      size="mobile-friendly"
                      onClick={handleAddToCart}
                      leftIcon={<ShoppingCart className="h-4 w-4" />}
                      className="h-10 sm:h-12 touch-target"
                    >
                      Add to Cart
                    </Button>
                    <Button
                      type="button"
                      variant="secondary"
                      size="mobile-friendly"
                      onClick={handleBuyNow}
                      className="h-10 sm:h-12 touch-target"
                    >
                      Buy Now
                    </Button>
                  </div>

                  {/* Customization button (if available) */}
                  {product.customizationAvailable && (
                    <Button
                      type="button"
                      variant="outline"
                      size="mobile-friendly"
                      onClick={() => setCustomizationFormOpen(true)}
                      leftIcon={<Scissors className="h-4 w-4" />}
                      className="h-10 sm:h-12 touch-target"
                    >
                      Contact for Customization
                    </Button>
                  )}
                </div>
              </div>

              <div className="pt-3 sm:pt-6 grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-4">
                <div className="flex items-center space-x-2 sm:space-x-3 p-2 sm:p-3 border border-badhees-100 rounded-lg bg-badhees-50">
                  <div className="flex-shrink-0">
                    <Truck className="h-4 w-4 sm:h-5 sm:w-5 text-badhees-600" />
                  </div>
                  <div>
                    <h4 className="text-xs font-medium text-badhees-800">Free Shipping</h4>
                    <p className="text-xs text-badhees-500">On orders over ₹15,000</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2 sm:space-x-3 p-2 sm:p-3 border border-badhees-100 rounded-lg bg-badhees-50">
                  <div className="flex-shrink-0">
                    <RefreshCw className="h-4 w-4 sm:h-5 sm:w-5 text-badhees-600" />
                  </div>
                  <div>
                    <h4 className="text-xs font-medium text-badhees-800">14-Day Returns</h4>
                    <p className="text-xs text-badhees-500">Hassle-free returns</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2 sm:space-x-3 p-2 sm:p-3 border border-badhees-100 rounded-lg bg-badhees-50">
                  <div className="flex-shrink-0">
                    <Shield className="h-4 w-4 sm:h-5 sm:w-5 text-badhees-600" />
                  </div>
                  <div>
                    <h4 className="text-xs font-medium text-badhees-800">Warranty</h4>
                    <p className="text-xs text-badhees-500">
                      <Link to="/warranty" className="text-badhees-600 hover:underline">
                        Check warranty
                      </Link>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="mt-8 sm:mt-16 border-t border-badhees-100 pt-4 sm:pt-8">
            <div className="flex flex-wrap border-b border-badhees-100">
              {(["description", "specifications", "reviews"] as const).map((tab) => (
                <button
                  type="button"
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={cn(
                    "px-3 sm:px-6 py-2 sm:py-3 text-xs sm:text-sm font-medium transition-colors border-b-2 -mb-px",
                    activeTab === tab
                      ? "border-badhees-accent text-badhees-800"
                      : "border-transparent text-badhees-500 hover:text-badhees-700"
                  )}
                >
                  {tab.charAt(0).toUpperCase() + tab.slice(1)}
                </button>
              ))}
            </div>

            <div className="py-4 sm:py-6">
              {activeTab === "description" && (
                <div className="prose max-w-none">
                  <p className="text-badhees-600 mb-4">
                    {product.description || `The ${product.name} represents the pinnacle of modern furniture design, combining aesthetic elegance with practical functionality. Crafted with meticulous attention to detail, this piece embodies our commitment to creating furniture that enhances your living space while standing the test of time.`}
                  </p>
                  <p className="text-badhees-600 mb-4">
                    Each {product.name} is handcrafted by skilled artisans using premium materials, ensuring exceptional quality and durability. The clean lines and minimalist form are thoughtfully designed to complement a variety of interior styles, from contemporary to transitional.
                  </p>
                  <p className="text-badhees-600">
                    More than just a beautiful addition to your home, the {product.name} is engineered for comfort and everyday use. Its ergonomic design provides optimal support, while the premium materials ensure longevity and resilience against daily wear.
                  </p>
                </div>
              )}

              {activeTab === "specifications" && (
                <div className="space-y-6">
                  {product.specifications && Object.keys(product.specifications).length > 0 ? (
                    <table className="w-full text-sm">
                      <tbody>
                        {Object.entries(product.specifications).map(([name, value]) => (
                          <tr key={name} className="border-b border-badhees-100">
                            <td className="py-3 font-medium text-badhees-800">{name}</td>
                            <td className="py-3 text-badhees-600">{value}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  ) : (
                    <div className="text-center py-8 text-badhees-500">
                      <p>No specifications available for this product.</p>
                    </div>
                  )}
                </div>
              )}

              {activeTab === "reviews" && (
                <div className="space-y-8">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
                    <div>
                      <div className="flex items-center">
                        <div className="text-4xl font-bold text-badhees-800 mr-4">
                          {(realRating || product.rating || 0).toFixed(1)}
                        </div>
                        <div>
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={cn(
                                  "h-5 w-5",
                                  i < Math.round(realRating || product.rating || 0) ? "text-yellow-400 fill-yellow-400" : "text-badhees-200"
                                )}
                              />
                            ))}
                            {ratingLoading && (
                              <Loader2 className="h-4 w-4 animate-spin text-badhees-400 ml-2" />
                            )}
                          </div>
                          <p className="text-sm text-badhees-500 mt-1">
                            Based on {realReviewCount || product.reviewCount || 0} {(realReviewCount || product.reviewCount || 0) === 1 ? 'review' : 'reviews'}
                          </p>
                        </div>
                      </div>
                    </div>
                    {isAuthenticated ? (
                      isCheckingPurchase ? (
                        <Button disabled className="bg-badhees-accent hover:bg-badhees-700 text-white">
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Checking...
                        </Button>
                      ) : hasPurchased ? (
                        <Button
                          onClick={() => setShowReviewForm(!showReviewForm)}
                          className="bg-badhees-accent hover:bg-badhees-700 text-white"
                        >
                          {showReviewForm ? 'Cancel' : 'Write a Review'}
                        </Button>
                      ) : (
                        <Button
                          disabled
                          className="bg-badhees-200 text-badhees-600"
                          title="You must purchase this product before reviewing it"
                        >
                          Purchase to Review
                        </Button>
                      )
                    ) : (
                      <Button
                        onClick={() => navigate('/login?redirect=' + encodeURIComponent(window.location.pathname))}
                        className="bg-badhees-accent hover:bg-badhees-700 text-white"
                      >
                        Login to Review
                      </Button>
                    )}
                  </div>

                  {/* Review Form */}
                  {showReviewForm && (
                    <div className="bg-badhees-50 p-6 rounded-lg mb-8">
                      <h3 className="text-lg font-medium text-badhees-800 mb-4">Write Your Review</h3>
                      <form onSubmit={handleReviewSubmit} className="space-y-4">
                        <div>
                          <Label htmlFor="rating" className="block text-sm font-medium text-badhees-700 mb-1">Rating</Label>
                          <div className="flex items-center space-x-1">
                            {[1, 2, 3, 4, 5].map((star) => (
                              <button
                                key={star}
                                type="button"
                                onClick={() => setReviewFormData(prev => ({ ...prev, rating: star }))}
                                className="focus:outline-none"
                                aria-label={`Rate ${star} out of 5 stars`}
                                title={`Rate ${star} out of 5 stars`}
                              >
                                <Star
                                  className={cn(
                                    "h-6 w-6",
                                    star <= reviewFormData.rating ? "text-yellow-400 fill-yellow-400" : "text-badhees-200"
                                  )}
                                />
                              </button>
                            ))}
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="title" className="block text-sm font-medium text-badhees-700 mb-1">Review Title</Label>
                          <Input
                            id="title"
                            value={reviewFormData.title}
                            onChange={(e) => setReviewFormData(prev => ({ ...prev, title: e.target.value }))}
                            placeholder="Summarize your experience"
                            className="w-full"
                          />
                        </div>
                        <div>
                          <Label htmlFor="comment" className="block text-sm font-medium text-badhees-700 mb-1">Your Review</Label>
                          <Textarea
                            id="comment"
                            value={reviewFormData.comment}
                            onChange={(e) => setReviewFormData(prev => ({ ...prev, comment: e.target.value }))}
                            placeholder="Share your experience with this product"
                            rows={4}
                            className="w-full"
                            required
                          />
                        </div>
                        <Button
                          type="submit"
                          className="bg-badhees-accent hover:bg-badhees-700 text-white"
                          disabled={isSubmittingReview}
                        >
                          {isSubmittingReview ? 'Submitting...' : 'Submit Review'}
                        </Button>
                      </form>
                    </div>
                  )}

                  {/* Reviews List */}
                  <div className="space-y-6">
                    {isLoadingReviews ? (
                      <div className="flex justify-center py-8">
                        <Loader2 className="h-8 w-8 animate-spin text-badhees-accent" />
                      </div>
                    ) : reviews.length > 0 ? (
                      reviews.map((review) => (
                        <div key={review.id} className="border-b border-badhees-100 pb-6">
                          <div className="flex justify-between items-start mb-3">
                            <div>
                              <h4 className="font-medium text-badhees-800">{review.userName || 'Anonymous'}</h4>
                              <p className="text-xs text-badhees-500">
                                {new Date(review.createdAt).toLocaleDateString('en-US', {
                                  year: 'numeric',
                                  month: 'long',
                                  day: 'numeric'
                                })}
                              </p>
                            </div>
                            <div className="flex">
                              {[...Array(5)].map((_, i) => (
                                <Star
                                  key={i}
                                  className={cn(
                                    "h-4 w-4",
                                    i < review.rating ? "text-yellow-400 fill-yellow-400" : "text-badhees-200"
                                  )}
                                />
                              ))}
                            </div>
                          </div>
                          {review.title && (
                            <h5 className="font-medium text-badhees-700 mb-1">{review.title}</h5>
                          )}
                          <p className="text-sm text-badhees-600">{review.comment}</p>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-badhees-500">No reviews yet. Be the first to review this product!</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Similar Products */}
          <div className="mt-8 sm:mt-16 border-t border-badhees-100 pt-6 sm:pt-12">
            <h2 className="text-xl sm:text-2xl font-bold text-badhees-800 mb-4 sm:mb-8">Similar Products</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
              {similarProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          </div>

          {/* Complementary Products */}
          <div className="mt-8 sm:mt-16 border-t border-badhees-100 pt-6 sm:pt-12">
            <h2 className="text-xl sm:text-2xl font-bold text-badhees-800 mb-4 sm:mb-8">Complete Your Space</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
              {complementaryProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          </div>

          {/* Frequently Bought Together */}
          <div className="mt-8 sm:mt-16 border-t border-badhees-100 pt-6 sm:pt-12 mb-8 sm:mb-16">
            <h2 className="text-xl sm:text-2xl font-bold text-badhees-800 mb-4 sm:mb-8">Frequently Bought Together</h2>
            <div className="bg-badhees-50 rounded-lg p-3 sm:p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3 sm:gap-6">
                {frequentlyBoughtTogether.map((bundleProduct) => (
                  <div key={bundleProduct.id} className="flex items-center space-x-3 sm:space-x-4">
                    <div
                      className="w-16 h-16 sm:w-20 sm:h-20 flex-shrink-0 bg-white rounded-md overflow-hidden border border-badhees-100 cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => navigate(`/products/${bundleProduct.id}`)}
                    >
                      <img src={bundleProduct.image} alt={bundleProduct.name} className="w-full h-full object-cover" />
                    </div>
                    <div>
                      <h4
                        className="text-xs sm:text-sm font-medium text-badhees-800 line-clamp-2 cursor-pointer hover:text-badhees-accent transition-colors"
                        onClick={() => navigate(`/products/${bundleProduct.id}`)}
                      >
                        {bundleProduct.name}
                      </h4>
                      <p className="text-xs sm:text-sm text-badhees-accent font-medium mt-0.5 sm:mt-1">₹{bundleProduct.price.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</p>
                      <button
                        type="button"
                        className="text-xs text-badhees-600 underline mt-0.5 sm:mt-1 hover:text-badhees-800 transition-colors"
                        aria-label={`Add ${bundleProduct.name} to cart`}
                        onClick={() => addToCart(bundleProduct, 1)}
                      >
                        Add to cart
                      </button>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4 sm:mt-6 flex flex-col md:flex-row justify-between items-center bg-white p-3 sm:p-4 rounded-lg border border-badhees-100">
                <div>
                  <p className="text-xs sm:text-sm font-medium text-badhees-800">Total Bundle Price:</p>
                  <div className="flex flex-wrap items-center mt-0.5 sm:mt-1">
                    <span className="font-bold text-base sm:text-xl text-badhees-accent">
                      ₹{(product.price + frequentlyBoughtTogether.reduce((acc, p) => acc + p.price, 0) * 0.9).toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}
                    </span>
                    <span className="ml-2 text-xs sm:text-sm line-through text-badhees-400">
                      ₹{(product.price + frequentlyBoughtTogether.reduce((acc, p) => acc + p.price, 0)).toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}
                    </span>
                    <span className="ml-2 text-xs bg-red-100 text-red-600 px-2 py-0.5 sm:py-1 rounded">Save 10%</span>
                  </div>
                </div>
                <button
                  type="button"
                  className="mt-3 md:mt-0 px-4 sm:px-6 py-2 bg-badhees-accent text-white rounded-md text-sm font-medium hover:bg-badhees-700 transition-colors"
                  aria-label="Add complete bundle to cart"
                  onClick={() => {
                    // Add the current product to cart
                    addToCart(product, 1);

                    // Add all bundle products to cart
                    frequentlyBoughtTogether.forEach(bundleProduct => {
                      addToCart(bundleProduct, 1);
                    });
                  }}
                >
                  Add Bundle to Cart
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Image Zoom Dialog */}
      <Dialog open={imageOpen} onOpenChange={handleCloseZoom}>
        <DialogContent className="max-w-5xl w-[90vw] h-[90vh] p-0 bg-black/90 border-none">
          <DialogTitle className="sr-only">Product Image Zoom</DialogTitle>
          <div className="relative w-full h-full flex items-center justify-center">
            <div className="absolute top-4 right-4 flex space-x-2 z-10">
              <button
                type="button"
                onClick={handleZoomIn}
                className="p-2 rounded-full bg-white/20 text-white hover:bg-white/30 transition-colors"
                aria-label="Zoom in"
              >
                <ZoomIn className="h-5 w-5" />
              </button>
              <button
                type="button"
                onClick={handleZoomOut}
                className="p-2 rounded-full bg-white/20 text-white hover:bg-white/30 transition-colors"
                aria-label="Zoom out"
              >
                <ZoomOut className="h-5 w-5" />
              </button>
              <button
                type="button"
                onClick={handleCloseZoom}
                className="p-2 rounded-full bg-white/20 text-white hover:bg-white/30 transition-colors"
                aria-label="Close zoom view"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div
              className="w-full h-full overflow-auto flex items-center justify-center p-8 cursor-grab"
            >
              <img
                src={productImages[activeImage]}
                alt={product.name}
                className={cn(
                  "max-w-full max-h-full object-contain product-image-zoom",
                  `product-image-zoom-${zoomLevel.toString().replace('.', '-')}`
                )}
              />
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Customization Request Form */}
      {product && (
        <CustomizationRequestForm
          isOpen={customizationFormOpen}
          onClose={() => setCustomizationFormOpen(false)}
          productId={product.id}
          productName={product.name}
        />
      )}

      <Footer />
    </div>
  );
};

export default ProductDetail;
