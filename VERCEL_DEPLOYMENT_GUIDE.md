# 🚀 Vercel Deployment Guide for The Badhees E-commerce

## 📋 **Pre-Deployment Checklist**

### ✅ **Security Fixes Applied**
- [x] Removed hardcoded API keys from all files
- [x] Secured Gmail app password
- [x] Added environment variable validation
- [x] Implemented proper error handling

### ✅ **API Routes Ready**
- [x] Converted Node.js server to Vercel API routes
- [x] `/api/razorpay/create-order.js` - Creates Razorpay orders
- [x] `/api/razorpay/verify-payment.js` - Verifies payments
- [x] `/api/razorpay/webhook.js` - Handles webhooks

## 🔧 **Environment Variables Setup**

### **Step 1: Vercel Dashboard Setup**

1. Go to [Vercel Dashboard](https://vercel.com/dashboard)
2. Select your project
3. Go to **Settings** → **Environment Variables**
4. Add the following variables:

```bash
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Razorpay Configuration (Backend - Keep Secret)
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_SECRET=your_razorpay_secret_key

# Frontend Razorpay Key (Public - Safe to expose)
VITE_RAZORPAY_KEY_ID=your_razorpay_key_id

# Production Settings
VITE_DEBUG_MODE=false
```

### **Step 2: Environment Types**
- Set all variables for **Production**, **Preview**, and **Development**
- Use different Razorpay keys for production vs test

## 🚀 **Deployment Steps**

### **Method 1: GitHub Integration (Recommended)**

1. **Connect Repository:**
   ```bash
   # Push your code to GitHub
   git add .
   git commit -m "Refactor to Vercel API routes"
   git push origin main
   ```

2. **Import to Vercel:**
   - Go to [Vercel Dashboard](https://vercel.com/new)
   - Import your GitHub repository
   - Vercel will auto-detect it as a Vite project

3. **Configure Build Settings:**
   - Framework Preset: **Vite**
   - Build Command: `npm run build`
   - Output Directory: `dist`
   - Install Command: `npm install`

### **Method 2: Vercel CLI**

1. **Install Vercel CLI:**
   ```bash
   npm i -g vercel
   ```

2. **Deploy:**
   ```bash
   cd final1
   vercel --prod
   ```

## 🔒 **Security Configuration**

### **Production Environment Variables**
Replace test credentials with production ones:

```bash
# Production Razorpay (Get from Razorpay Dashboard)
RAZORPAY_KEY_ID=rzp_live_your_live_key
RAZORPAY_SECRET=your_live_secret_key
VITE_RAZORPAY_KEY_ID=rzp_live_your_live_key

# Production Supabase (if different)
VITE_SUPABASE_URL=your_production_supabase_url
VITE_SUPABASE_ANON_KEY=your_production_anon_key
```

## 🧪 **Testing Your Deployment**

### **1. Test API Routes**
```bash
# Test order creation
curl -X POST https://your-domain.vercel.app/api/razorpay/create-order \
  -H "Content-Type: application/json" \
  -d '{"amount": 100, "receipt": "test_receipt_123"}'

# Should return: {"success": true, "data": {...}}
```

### **2. Test Frontend Integration**
1. Visit your deployed site
2. Add items to cart
3. Proceed to checkout
4. Test payment flow (use test cards)

### **3. Monitor Logs**
```bash
# View function logs
vercel logs your-deployment-url
```

## 🔧 **Troubleshooting**

### **Common Issues:**

1. **Environment Variables Not Loading:**
   - Ensure variables are set in Vercel dashboard
   - Redeploy after adding variables
   - Check variable names match exactly

2. **API Routes Not Working:**
   - Verify file structure: `/api/razorpay/*.js`
   - Check function logs in Vercel dashboard
   - Ensure proper export syntax

3. **CORS Issues:**
   - Vercel.json handles CORS headers
   - API routes include CORS headers
   - Check browser network tab for errors

4. **Payment Verification Fails:**
   - Verify RAZORPAY_SECRET is set correctly
   - Check signature generation logic
   - Monitor API route logs

## 📊 **Performance Optimization**

### **Already Implemented:**
- ✅ Code splitting with lazy loading
- ✅ Image optimization
- ✅ React Query caching
- ✅ Tab visibility management
- ✅ Performance monitoring

### **Vercel Specific:**
- ✅ Edge functions for fast API responses
- ✅ CDN for static assets
- ✅ Automatic compression

## 🎯 **Post-Deployment Tasks**

1. **Update Razorpay Webhook URL:**
   - Go to Razorpay Dashboard
   - Set webhook URL: `https://your-domain.vercel.app/api/razorpay/webhook`

2. **Test Email Functions:**
   - Verify Supabase Edge Functions are deployed
   - Test order confirmation emails

3. **Monitor Performance:**
   - Use Vercel Analytics
   - Monitor API response times
   - Check error rates

## 🔄 **Continuous Deployment**

Once connected to GitHub:
- Every push to `main` triggers automatic deployment
- Preview deployments for pull requests
- Rollback capability through Vercel dashboard

## 📞 **Support**

If you encounter issues:
1. Check Vercel function logs
2. Verify environment variables
3. Test API routes individually
4. Monitor browser console for errors

Your deployment should now be live and fully functional! 🎉
