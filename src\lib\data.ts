// Define the Product type that will be used throughout the application
export interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  salePrice?: number;
  isSale?: boolean;
  isNew?: boolean;
  image: string;
  images?: string[];
  category: string;
  stockStatus?: "in_stock" | "out_of_stock" | "low_stock";
  // Add these properties to make it compatible with editorProductsService.Product
  status?: 'active' | 'draft' | 'deleted';
  stock?: number;
  sku?: string;
  specifications?: { [key: string]: string };
  customizationAvailable?: boolean;
}

// Featured products data
export const featuredProducts: Product[] = [
  {
    id: "1",
    name: "Comfort Lounge Chair",
    price: 599,
    salePrice: 499,
    isSale: true,
    image: "https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    images: [
      "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
      "https://images.unsplash.com/photo-1618220048045-10a6dbdf83e0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
    ],
    category: "Chair",
    stockStatus: "in_stock",
    specifications: {
      "Material": "Premium fabric, solid walnut",
      "Dimensions": "31.5\"W x 32.5\"D x 31\"H",
      "Weight Capacity": "300 lbs",
      "Assembly": "Minimal assembly required",
      "Warranty": "5-year manufacturer warranty"
    }
  },
  {
    id: "2",
    name: "Modern Coffee Table",
    price: 349,
    image: "https://images.unsplash.com/photo-1533090161767-e6ffed986c88?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    images: [
      "https://images.unsplash.com/photo-1499933374294-4584851497cc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
      "https://images.unsplash.com/photo-1567016376408-0226e4d0c1ea?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
    ],
    category: "Table",
    stockStatus: "in_stock",
    specifications: {
      "Material": "Tempered glass, solid oak base",
      "Dimensions": "42\"L x 24\"W x 18\"H",
      "Weight": "45 lbs",
      "Care": "Wipe clean with glass cleaner",
      "Style": "Contemporary, minimalist"
    }
  },
  {
    id: "3",
    name: "Scandinavian Sofa",
    price: 1299,
    salePrice: 999,
    isSale: true,
    image: "https://images.unsplash.com/photo-1493663284031-b7e3aefcae8e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    images: [
      "https://images.unsplash.com/photo-1540574163026-643ea20ade25?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
      "https://images.unsplash.com/photo-1555041469-a586c61ea9bc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
    ],
    category: "Sofa",
    stockStatus: "low_stock",
    specifications: {
      "Material": "Leather, plush fabric",
      "Dimensions": "60\"W x 80\"D x 30\"H",
      "Weight Capacity": "500 lbs",
      "Assembly": "Requires assembly",
      "Warranty": "3-year manufacturer warranty"
    }
  },
  {
    id: "4",
    name: "Minimalist Desk",
    price: 499,
    isNew: true,
    image: "https://images.unsplash.com/photo-1518051870910-a46e30d9db16?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    category: "Table",
    stockStatus: "in_stock",
    specifications: {
      "Material": "Solid wood, tempered glass",
      "Dimensions": "36\"L x 24\"W x 24\"H",
      "Weight": "25 lbs",
      "Care": "Wipe clean with microfiber cloth",
      "Style": "Modern, minimalist"
    }
  },
  {
    id: "5",
    name: "Contemporary Dining Set",
    price: 1899,
    image: "https://images.unsplash.com/photo-1532372576444-dda954194ad0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    category: "Table",
    stockStatus: "in_stock",
    specifications: {
      "Material": "Solid wood, tempered glass",
      "Dimensions": "72\"L x 38\"W x 30\"H",
      "Weight": "45 lbs",
      "Care": "Wipe clean with microfiber cloth",
      "Style": "Contemporary, minimalist"
    }
  },
  {
    id: "6",
    name: "Artisan Bookshelf",
    price: 899,
    salePrice: 699,
    isSale: true,
    image: "https://images.unsplash.com/photo-1594026112284-02bb6f3352fe?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    category: "Storage",
    stockStatus: "out_of_stock",
    specifications: {
      "Material": "Wood, metal",
      "Dimensions": "60\"W x 30\"D x 120\"H",
      "Weight Capacity": "100 lbs",
      "Assembly": "Requires assembly",
      "Warranty": "2-year manufacturer warranty"
    }
  }
];

// All products data
export const products: Product[] = [
  ...featuredProducts,
  {
    id: "7",
    name: "Ergonomic Office Chair",
    price: 449,
    image: "https://images.unsplash.com/photo-1505843490538-5133c6c7d0e1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    category: "Chair",
    stockStatus: "in_stock",
    specifications: {
      "Material": "Mesh back, padded seat",
      "Adjustability": "Height, armrest, tilt, lumbar support",
      "Weight Capacity": "275 lbs",
      "Base": "5-wheel rolling base with 360° swivel",
      "Features": "Breathable mesh, padded armrests"
    }
  },
  {
    id: "8",
    name: "Rustic Dining Table",
    price: 1299,
    image: "https://images.unsplash.com/photo-1604578762246-41134e37f9cc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    category: "Table",
    stockStatus: "in_stock",
    specifications: {
      "Material": "Solid reclaimed pine wood",
      "Finish": "Hand-distressed, natural wood sealant",
      "Dimensions": "72\"L x 38\"W x 30\"H",
      "Seating": "Seats up to 8 people",
      "Style": "Farmhouse, rustic"
    }
  },
  {
    id: "9",
    name: "Platform Bed Frame",
    price: 899,
    salePrice: 799,
    isSale: true,
    image: "https://images.unsplash.com/photo-1505693416388-ac5ce068fe85?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    category: "Bed",
    stockStatus: "low_stock",
    specifications: {
      "Material": "Solid wood, metal",
      "Dimensions": "60\"W x 80\"D x 60\"H",
      "Weight Capacity": "500 lbs",
      "Assembly": "Requires assembly",
      "Warranty": "3-year manufacturer warranty"
    }
  },
  {
    id: "10",
    name: "Accent Armchair",
    price: 649,
    isNew: true,
    image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    category: "Chair",
    stockStatus: "in_stock",
    specifications: {
      "Material": "Leather, plush fabric",
      "Dimensions": "36\"W x 36\"D x 36\"H",
      "Weight Capacity": "200 lbs",
      "Assembly": "Minimal assembly required",
      "Warranty": "5-year manufacturer warranty"
    }
  },
  {
    id: "11",
    name: "Modular Storage System",
    price: 1499,
    image: "https://images.unsplash.com/photo-1595428774223-ef52624120d2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    category: "Storage",
    stockStatus: "in_stock",
    specifications: {
      "Material": "Wood, metal",
      "Dimensions": "60\"W x 30\"D x 120\"H",
      "Weight Capacity": "100 lbs",
      "Assembly": "Requires assembly",
      "Warranty": "2-year manufacturer warranty"
    }
  },
  {
    id: "12",
    name: "Velvet Accent Sofa",
    price: 1199,
    salePrice: 999,
    isSale: true,
    image: "https://images.unsplash.com/photo-1555041469-a586c61ea9bc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    category: "Sofa",
    stockStatus: "out_of_stock",
    specifications: {
      "Material": "Leather, plush fabric",
      "Dimensions": "60\"W x 80\"D x 60\"H",
      "Weight Capacity": "500 lbs",
      "Assembly": "Requires assembly",
      "Warranty": "3-year manufacturer warranty"
    }
  },
  {
    id: "13",
    name: "Solid Wood Dresser",
    price: 849,
    image: "https://images.unsplash.com/photo-1591129841117-3adfd313a592?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    category: "Storage",
    stockStatus: "in_stock",
    specifications: {
      "Material": "Wood, metal",
      "Dimensions": "60\"W x 30\"D x 120\"H",
      "Weight Capacity": "100 lbs",
      "Assembly": "Requires assembly",
      "Warranty": "2-year manufacturer warranty"
    }
  },
  {
    id: "14",
    name: "Coastal Night Stand",
    price: 299,
    isNew: true,
    image: "https://images.unsplash.com/photo-1551215717-05fbd363e8a5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    category: "Table",
    stockStatus: "in_stock",
    specifications: {
      "Material": "Solid wood, tempered glass",
      "Dimensions": "36\"L x 24\"W x 24\"H",
      "Weight": "25 lbs",
      "Care": "Wipe clean with microfiber cloth",
      "Style": "Modern, minimalist"
    }
  },
  {
    id: "15",
    name: "Wall Shelf Unit",
    price: 399,
    salePrice: 349,
    isSale: true,
    image: "https://images.unsplash.com/photo-1616627974167-d09875c75e8e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    category: "Storage",
    stockStatus: "low_stock",
    specifications: {
      "Material": "Wood, metal",
      "Dimensions": "60\"W x 30\"D x 120\"H",
      "Weight Capacity": "100 lbs",
      "Assembly": "Requires assembly",
      "Warranty": "2-year manufacturer warranty"
    }
  },
  {
    id: "16",
    name: "Mid-Century Side Table",
    price: 249,
    image: "https://images.unsplash.com/photo-1499933374294-4584851497cc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    category: "Table",
    stockStatus: "in_stock",
    specifications: {
      "Material": "Solid wood, tempered glass",
      "Dimensions": "36\"L x 24\"W x 24\"H",
      "Weight": "25 lbs",
      "Care": "Wipe clean with microfiber cloth",
      "Style": "Modern, minimalist"
    }
  }
].map(product => {
  // Get the product's stockStatus or default to "in_stock"
  const stockStatus = product.stockStatus || "in_stock";

  // Make sure stockStatus is one of the allowed values
  const validatedStockStatus: "in_stock" | "out_of_stock" | "low_stock" =
    (stockStatus === "in_stock" || stockStatus === "out_of_stock" || stockStatus === "low_stock")
      ? stockStatus
      : "in_stock";

  return {
    ...product,
    status: 'active' as const,
    stock: 10,
    sku: `SKU-${product.id}`,
    stockStatus: validatedStockStatus,
    // Add default specifications if not provided
    specifications: product.specifications || {
      "Dimensions": "Various sizes available",
      "Material": "Premium quality materials",
      "Care": "Refer to product manual for care instructions",
      "Origin": "Designed and crafted with attention to detail"
    }
  };
});

// Category data for the showcase
export const categories = [
  {
    id: "cat1",
    name: "Sofas",
    image: "https://images.unsplash.com/photo-1540574163026-643ea20ade25?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    description: "Relax in style with our premium collection of sofas designed for comfort and elegance.",
    itemCount: 12
  },
  {
    id: "cat2",
    name: "Chairs",
    image: "https://images.unsplash.com/photo-1586158291800-2665f07bba79?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    description: "Find the perfect seat with our diverse range of chairs, from dining to lounge.",
    itemCount: 15
  },
  {
    id: "cat3",
    name: "Tables",
    image: "https://images.unsplash.com/photo-1577140917170-285929fb55b7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    description: "Discover beautifully crafted tables for every space in your home.",
    itemCount: 20
  },
  {
    id: "cat4",
    name: "Bedroom",
    image: "https://images.unsplash.com/photo-1505693416388-ac5ce068fe85?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    description: "Transform your bedroom into a peaceful retreat with our quality furniture.",
    itemCount: 18
  }
];
