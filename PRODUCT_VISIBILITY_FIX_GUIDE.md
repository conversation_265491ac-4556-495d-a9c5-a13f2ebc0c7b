# Product Visibility Fix Guide

## Issues Identified

Based on the console errors, the main problems are:

1. **No products found in database** - The products table is empty
2. **400 Bad Request errors** - UUID vs integer mismatch in product reviews
3. **Invalid input syntax for type uuid: "1"** - Hardcoded integer IDs being used instead of UUIDs

## Root Cause

The database has the table structure but **no actual product data**. The application is trying to fetch products from an empty database.

## Step-by-Step Fix

### Step 1: Populate Database with Products (CRITICAL)

**Execute this in Supabase SQL Editor:**

```sql
-- Copy and paste the entire content of complete_database_setup.sql
-- This will create categories, products, and product images with sample data
```

**What this script does:**
- ✅ Creates categories table with 6 furniture categories
- ✅ Creates products table with proper UUID structure
- ✅ Inserts 6 sample products with realistic data
- ✅ Creates product_images table and links images to products
- ✅ Uses proper UUID format for all relationships
- ✅ Includes specifications, pricing, and stock information

### Step 2: Verify Database Population

After running the SQL script, check in Supabase:

1. **Tables Tab**: Verify these tables exist and have data:
   - `categories` (should have 6 rows)
   - `products` (should have 6 rows)
   - `product_images` (should have 7 rows)

2. **SQL Editor**: Run this verification query:
   ```sql
   SELECT 
     (SELECT COUNT(*) FROM categories) as categories,
     (SELECT COUNT(*) FROM products) as products,
     (SELECT COUNT(*) FROM product_images) as images;
   ```

3. **Expected Results**: Should show categories: 6, products: 6, images: 7

### Step 3: Clear Browser Cache and Test

1. **Hard Refresh**: Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)
2. **Clear Console**: Click the clear button in browser console
3. **Navigate to Homepage**: Should now show products

## Expected Results After Fix

### Console Logs Should Show:
```
[ProductService] Successfully fetched X products from database
[ProductService] Products loaded with proper UUID format
```

### Website Should Display:
- ✅ Products visible on homepage
- ✅ Product cards with images, names, and prices
- ✅ Categories working properly
- ✅ Product detail pages accessible
- ✅ No 400 or 500 errors in console

## Sample Products Created

The script creates these products:

1. **Comfort Lounge Chair** - $599 (Sale: $499)
2. **Modern Dining Table** - $899
3. **Executive Office Chair** - $449 (Sale: $399)
4. **Scandinavian Sofa** - $1,299
5. **Industrial Bookshelf** - $349 (Sale: $299)
6. **Pendant Light Fixture** - $199

## Troubleshooting Common Issues

### Issue: "Table 'products' doesn't exist"
**Solution**: Re-run the `complete_database_setup.sql` script

### Issue: Still no products showing
**Solution**: Check browser console for new errors and verify:
```sql
SELECT id, name, price FROM products LIMIT 5;
```

### Issue: Images not loading
**Solution**: The script uses Unsplash URLs. If images don't load, check your internet connection.

### Issue: UUID format errors persist
**Solution**: The frontend code has been updated to handle UUIDs properly. Clear browser cache completely.

## Advanced Debugging

### Check Product Service:
Open browser console and look for:
- `[ProductService]` log messages
- Any remaining 400/500 errors
- Network tab for failed API calls

### Verify Product Structure:
```sql
-- Check product structure
SELECT 
  id,
  name,
  price,
  category_id,
  stock,
  pg_typeof(id) as id_type
FROM products 
LIMIT 3;
```

### Test Product Images:
```sql
-- Check product images
SELECT 
  p.name,
  pi.image_url,
  pi.is_primary
FROM products p
JOIN product_images pi ON p.id = pi.product_id
ORDER BY p.name, pi.sort_order;
```

## Recovery Options

### Option 1: Add More Products
If you want to add more products, use this template:
```sql
INSERT INTO products (id, name, description, price, category_id, stock, sku) VALUES
  (uuid_generate_v4(), 'Your Product Name', 'Description', 299.00, 'category-uuid-here', 10, 'SKU-001');
```

### Option 2: Reset and Rebuild
If something goes wrong:
```sql
-- Clear all data and start fresh
DELETE FROM product_images;
DELETE FROM products;
DELETE FROM categories;
-- Then re-run complete_database_setup.sql
```

## Success Indicators

✅ **Database**: Products table has 6+ rows
✅ **Frontend**: Products visible on homepage
✅ **Console**: No "No products found" warnings
✅ **Images**: Product images loading properly
✅ **Navigation**: Can click on products and view details
✅ **Categories**: Category filtering works

## Next Steps After Fix

Once products are visible:

1. **Test Product Details**: Click on individual products
2. **Test Categories**: Use category filters
3. **Test Search**: Search for product names
4. **Test Cart**: Add products to cart
5. **Test Checkout**: Complete a test order

The key is that **the database was empty** - once populated with products, the entire application should work properly.
