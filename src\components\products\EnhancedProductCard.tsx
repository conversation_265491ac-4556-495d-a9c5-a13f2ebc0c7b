import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Share2, ShoppingCart, Star, ArrowRight, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button-system';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { useCart } from '@/context/SupabaseCartContext';
import { toast } from '@/hooks/use-toast';
import { shareProduct } from '@/utils/shareUtils';
import { useProductRating } from '@/hooks/useProductRating';
import './product-card.css';

interface ProductCardProps {
  product: any;
  className?: string;
}

// Define a more specific type for the product
interface Product {
  id: string;
  name: string;
  price: number;
  image: string;
  images?: string[] | string;
  rating?: number;
  reviewCount?: number;
  salePrice?: number;
  originalPrice?: number;
  customizationAvailable?: boolean;
  category?: string;
  description?: string;
  status?: string;
  stock?: number;
}

function EnhancedProductCard({ product, className = '' }: ProductCardProps) {
  const { addToCart } = useCart();
  const navigate = useNavigate();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const [touchStartX, setTouchStartX] = useState<number | null>(null);

  // Get real-time rating data
  const { rating, reviewCount, isLoading: ratingLoading } = useProductRating(product.id);

  // Memoize the product images array to prevent unnecessary recalculations
  const productImages = useMemo(() => {
    return product.images ?
      (Array.isArray(product.images) ? product.images : [product.images, product.image]) :
      [product.image];
  }, [product.images, product.image]);

  // Memoize event handlers to prevent unnecessary re-renders
  const handleAddToCart = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    addToCart(product, 1);
  }, [addToCart, product]);

  const handleShare = useCallback(async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const success = await shareProduct(product);

    if (success) {
      toast({
        title: "Shared successfully",
        description: navigator.share ? "Product shared successfully" : "Product link copied to clipboard",
      });
    } else {
      toast({
        title: "Sharing failed",
        description: "Unable to share this product. Please try again.",
        variant: "destructive",
      });
    }
  }, [product]);

  // Auto-rotate images every 4 seconds if there are multiple images
  useEffect(() => {
    if (productImages.length <= 1 || isHovered) return;

    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        prevIndex === productImages.length - 1 ? 0 : prevIndex + 1
      );
    }, 4000);

    return () => clearInterval(interval);
  }, [productImages.length, isHovered]);

  // Handle touch events for swiping on mobile - memoized with useCallback
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    setTouchStartX(e.touches[0].clientX);
  }, []);

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    if (touchStartX === null) return;

    const touchEndX = e.changedTouches[0].clientX;
    const diff = touchStartX - touchEndX;

    // Swipe threshold of 50px
    if (Math.abs(diff) > 50) {
      if (diff > 0) {
        // Swipe left - next image
        setCurrentImageIndex((prevIndex) =>
          prevIndex === productImages.length - 1 ? 0 : prevIndex + 1
        );
      } else {
        // Swipe right - previous image
        setCurrentImageIndex((prevIndex) =>
          prevIndex === 0 ? productImages.length - 1 : prevIndex - 1
        );
      }
    }

    setTouchStartX(null);
  }, [touchStartX, productImages.length]);

  const handleBuyNow = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Add to cart first
    addToCart(product, 1);

    // Navigate to cart/checkout
    navigate('/cart');

    toast({
      title: 'Ready to checkout',
      description: `${product.name} has been added to your cart.`,
    });
  }, [addToCart, navigate, product]);

  // Format price with Indian Rupee symbol and thousands separator - memoized
  const formattedPrice = useMemo(() => {
    const price = product.isSale && product.salePrice ? product.salePrice : product.price;
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(price);
  }, [product.price, product.salePrice, product.isSale]);

  const formattedOriginalPrice = useMemo(() => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(product.price);
  }, [product.price]);

  return (
    <Card
      className={`group overflow-hidden transition-all duration-300 hover:shadow-md bg-transparent ${className}`}
    >
      <Link to={`/products/${product.id}`} className="block h-full">
        <div
          className="relative aspect-square overflow-hidden rounded-lg"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          onTouchStart={handleTouchStart}
          onTouchEnd={handleTouchEnd}
        >
          {/* Product image carousel - simplified approach */}
          <div className="relative w-full h-full">
            {productImages.map((img: string, index: number) => (
              <img
                key={index}
                src={img}
                alt={`${product.name} - View ${index + 1}`}
                className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-500 ${
                  index === currentImageIndex ? 'opacity-100' : 'opacity-0'
                }`}
                loading={index === 0 ? "eager" : "lazy"}
                decoding="async"
                onError={(e) => {
                  // Set a fallback image if loading fails
                  (e.target as HTMLImageElement).src = '/placeholder.svg';
                }}
              />
            ))}
          </div>

          {/* Share button - visible on mobile and on hover */}
          <div className="absolute top-3 right-3 flex flex-col gap-2 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity">
            <Button
              variant="secondary"
              size="icon-sm"
              rounded="full"
              className="bg-white bg-opacity-80 hover:bg-opacity-100 touch-target"
              aria-label="Share product"
              onClick={handleShare}
            >
              <Share2 className="h-4 w-4" />
            </Button>
          </div>

          {/* Customizable badge */}
          {product.customizationAvailable && (
            <div className="absolute top-3 right-12 z-10">
              <span className="text-xs bg-badhees-100 text-badhees-800 px-2 py-1 rounded-full">
                Customizable
              </span>
            </div>
          )}

          {/* Sale badge */}
          {product.salePrice && (
            <div className="absolute top-3 left-3 bg-red-500 text-white text-xs font-bold px-3 py-1.5 rounded-full">
              SALE
            </div>
          )}
        </div>

        <CardContent className="p-4 bg-transparent">
          <div className="mb-1 flex items-center justify-between">
            <h3 className="font-medium text-badhees-800 line-clamp-1 text-base">{product.name}</h3>
          </div>

          <div className="flex items-center gap-1 mb-2">
            <div className="flex">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  className={`h-3.5 w-3.5 ${
                    star <= Math.round(rating)
                      ? 'text-yellow-400 fill-yellow-400'
                      : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
            <span className="text-xs text-gray-500">
              ({reviewCount})
            </span>
            {ratingLoading && (
              <span className="text-xs text-gray-400 ml-1">Loading...</span>
            )}
          </div>

          <div className="flex items-baseline gap-2">
            <span className="text-lg font-bold text-badhees-accent">
              {formattedPrice}
            </span>
            {product.isSale && product.salePrice && (
              <span className="text-sm text-gray-500 line-through">
                {formattedOriginalPrice}
              </span>
            )}
          </div>


        </CardContent>

        <CardFooter className="p-4 pt-0 bg-transparent">
          <div className="w-full flex items-center gap-2">
            {/* Buy Now button - 40% width */}
            <Button
              variant="primary"
              size="mobile-friendly"
              className="h-12 touch-target flex-grow basis-[40%]"
              onClick={handleBuyNow}
            >
              Buy Now
            </Button>

            {/* View Details button - 40% width */}
            <Button
              variant="outline"
              size="mobile-friendly"
              className="h-12 touch-target flex-grow basis-[40%]"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                window.location.href = `/products/${product.id}`;
              }}
            >
              View Details
            </Button>

            {/* Add to Cart icon button - 20% width */}
            <Button
              variant="secondary"
              size="icon-lg"
              rounded="full"
              className="h-12 w-12 touch-target flex-shrink-0 bg-badhees-accent text-white hover:bg-badhees-700"
              aria-label="Add to cart"
              onClick={handleAddToCart}
            >
              <ShoppingCart className="h-5 w-5" />
            </Button>
          </div>
        </CardFooter>
      </Link>
    </Card>
  );
}

// Memoize the component for better performance
const MemoizedEnhancedProductCard = React.memo(EnhancedProductCard);

// Use displayName for better debugging
MemoizedEnhancedProductCard.displayName = 'EnhancedProductCard';

export default MemoizedEnhancedProductCard;
