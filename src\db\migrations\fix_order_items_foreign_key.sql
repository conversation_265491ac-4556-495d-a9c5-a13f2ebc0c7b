-- Fix the foreign key constraint on order_items table
-- to add ON DELETE CASCADE for product_id

-- First, drop the existing constraint
ALTER TABLE order_items DROP CONSTRAINT IF EXISTS order_items_product_id_fkey;

-- Then, add it back with ON DELETE CASCADE
ALTER TABLE order_items 
  ADD CONSTRAINT order_items_product_id_fkey 
  FOREIGN KEY (product_id) 
  REFERENCES products(id) 
  ON DELETE CASCADE;

-- Add a comment to explain the purpose of this migration
COMMENT ON CONSTRAINT order_items_product_id_fkey ON order_items IS 
  'Foreign key to products table with cascade delete - when a product is deleted, all related order items are also deleted';
