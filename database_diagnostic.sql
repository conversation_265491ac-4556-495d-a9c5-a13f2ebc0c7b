-- ============================================================================
-- DATABASE DIAGNOSTIC SCRIPT
-- Run this first to understand the current state of your database
-- ============================================================================

SELECT 'DATABASE DIAGNOSTIC REPORT' as title;
SELECT '=========================' as separator;

-- ============================================================================
-- STEP 1: CHECK TABLE EXISTENCE
-- ============================================================================

SELECT 'STEP 1: Checking if required tables exist' as step;

SELECT 
  table_name,
  CASE 
    WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_name = t.table_name) 
    THEN '✅ EXISTS' 
    ELSE '❌ MISSING' 
  END as status
FROM (VALUES 
  ('categories'),
  ('products'),
  ('product_images'),
  ('orders'),
  ('order_items'),
  ('product_reviews'),
  ('user_profiles')
) AS t(table_name);

-- ============================================================================
-- STEP 2: CHECK DATA COUNTS
-- ============================================================================

SELECT 'STEP 2: Checking data counts in each table' as step;

SELECT 
  'categories' as table_name,
  CASE 
    WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'categories')
    THEN (SELECT COUNT(*)::text FROM categories)
    ELSE 'TABLE MISSING'
  END as row_count;

SELECT 
  'products' as table_name,
  CASE 
    WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'products')
    THEN (SELECT COUNT(*)::text FROM products)
    ELSE 'TABLE MISSING'
  END as row_count;

SELECT 
  'product_images' as table_name,
  CASE 
    WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'product_images')
    THEN (SELECT COUNT(*)::text FROM product_images)
    ELSE 'TABLE MISSING'
  END as row_count;

SELECT 
  'orders' as table_name,
  CASE 
    WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'orders')
    THEN (SELECT COUNT(*)::text FROM orders)
    ELSE 'TABLE MISSING'
  END as row_count;

-- ============================================================================
-- STEP 3: CHECK PRODUCT TABLE STRUCTURE
-- ============================================================================

SELECT 'STEP 3: Checking products table structure' as step;

SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'products' 
ORDER BY ordinal_position;

-- ============================================================================
-- STEP 4: SAMPLE DATA CHECK
-- ============================================================================

SELECT 'STEP 4: Sample data from products table (if exists)' as step;

DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'products') THEN
    IF (SELECT COUNT(*) FROM products) > 0 THEN
      RAISE NOTICE 'Products table has data - showing sample:';
    ELSE
      RAISE NOTICE '❌ PRODUCTS TABLE IS EMPTY - This is the main issue!';
    END IF;
  ELSE
    RAISE NOTICE '❌ PRODUCTS TABLE DOES NOT EXIST';
  END IF;
END $$;

-- Show sample products if they exist
SELECT 
  id,
  name,
  price,
  stock,
  pg_typeof(id) as id_type
FROM products 
LIMIT 3;

-- ============================================================================
-- STEP 5: CHECK PRODUCT IMAGES
-- ============================================================================

SELECT 'STEP 5: Checking product images' as step;

DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'product_images') THEN
    IF (SELECT COUNT(*) FROM product_images) > 0 THEN
      RAISE NOTICE 'Product images table has data';
    ELSE
      RAISE NOTICE '❌ PRODUCT_IMAGES TABLE IS EMPTY';
    END IF;
  ELSE
    RAISE NOTICE '❌ PRODUCT_IMAGES TABLE DOES NOT EXIST';
  END IF;
END $$;

-- ============================================================================
-- STEP 6: DIAGNOSIS SUMMARY
-- ============================================================================

SELECT 'STEP 6: DIAGNOSIS SUMMARY' as step;

DO $$
DECLARE
  products_exist boolean;
  products_count integer;
  categories_count integer;
  images_count integer;
BEGIN
  -- Check if products table exists and has data
  SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'products') INTO products_exist;
  
  IF products_exist THEN
    SELECT COUNT(*) FROM products INTO products_count;
    SELECT COUNT(*) FROM categories INTO categories_count;
    SELECT COUNT(*) FROM product_images INTO images_count;
    
    RAISE NOTICE '=== DIAGNOSIS RESULTS ===';
    RAISE NOTICE 'Products table exists: %', products_exist;
    RAISE NOTICE 'Products count: %', products_count;
    RAISE NOTICE 'Categories count: %', categories_count;
    RAISE NOTICE 'Images count: %', images_count;
    RAISE NOTICE '';
    
    IF products_count = 0 THEN
      RAISE NOTICE '🔥 MAIN ISSUE FOUND: Products table is EMPTY!';
      RAISE NOTICE '📋 SOLUTION: Run complete_database_setup.sql to populate products';
      RAISE NOTICE '';
    ELSE
      RAISE NOTICE '✅ Products table has data - issue might be elsewhere';
    END IF;
    
  ELSE
    RAISE NOTICE '🔥 CRITICAL: Products table does not exist!';
    RAISE NOTICE '📋 SOLUTION: Run complete_database_setup.sql to create and populate tables';
  END IF;
END $$;

-- ============================================================================
-- STEP 7: RECOMMENDED ACTIONS
-- ============================================================================

SELECT 'STEP 7: RECOMMENDED ACTIONS' as step;

DO $$
DECLARE
  products_count integer := 0;
BEGIN
  -- Get product count if table exists
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'products') THEN
    SELECT COUNT(*) FROM products INTO products_count;
  END IF;
  
  RAISE NOTICE '=== RECOMMENDED ACTIONS ===';
  
  IF products_count = 0 THEN
    RAISE NOTICE '1. 🚨 URGENT: Run complete_database_setup.sql immediately';
    RAISE NOTICE '2. 🔄 Clear browser cache after running the script';
    RAISE NOTICE '3. 🌐 Refresh the website to see products';
    RAISE NOTICE '4. ✅ Verify products appear on homepage';
  ELSE
    RAISE NOTICE '1. ✅ Database has products - check frontend code';
    RAISE NOTICE '2. 🔍 Check browser console for other errors';
    RAISE NOTICE '3. 🔄 Clear browser cache and refresh';
  END IF;
  
  RAISE NOTICE '';
  RAISE NOTICE 'For detailed instructions, see: PRODUCT_VISIBILITY_FIX_GUIDE.md';
END $$;

SELECT 'DIAGNOSTIC COMPLETE' as result;
