-- Check if products exist in the database
-- Run this in Supabase SQL Editor

-- 1. Check if products table exists and has data
SELECT 
  'products' as table_name,
  COUNT(*) as total_count,
  COUNT(CASE WHEN status = 'active' THEN 1 END) as active_count
FROM products;

-- 2. Show sample products
SELECT 
  id,
  name,
  price,
  status,
  created_at
FROM products 
ORDER BY created_at DESC 
LIMIT 5;

-- 3. Check categories table
SELECT 
  'categories' as table_name,
  COUNT(*) as count
FROM categories;

-- 4. Check product_images table
SELECT 
  'product_images' as table_name,
  COUNT(*) as count
FROM product_images;

-- 5. Check if the query that frontend uses works
SELECT 
  p.*,
  c.id as category_id,
  c.name as category_name,
  pi.image_url,
  prs.average_rating,
  prs.total_reviews
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN product_images pi ON p.id = pi.product_id
LEFT JOIN product_ratings_summary prs ON p.id = prs.product_id
WHERE p.status = 'active'
ORDER BY p.created_at DESC
LIMIT 3;
