-- Create a function to check if a user has purchased a product
CREATE OR REPLACE FUNCTION has_user_purchased_product(user_id UUID, product_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  has_purchased BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM orders o
    JOIN order_items oi ON o.id = oi.order_id
    WHERE o.user_id = user_id
    AND oi.product_id = product_id
    AND o.status IN ('delivered', 'shipped') -- Only count delivered or shipped orders
  ) INTO has_purchased;
  
  RETURN has_purchased;
END;
$$ LANGUAGE plpgsql;

-- Update the RLS policy for product_reviews to enforce purchase verification
DROP POLICY IF EXISTS "Users can insert their own reviews" ON product_reviews;

-- Create a new policy that checks if the user has purchased the product
CREATE POLICY "Users can insert reviews for purchased products" 
  ON product_reviews FOR INSERT 
  WITH CHECK (
    auth.uid() = user_id AND
    has_user_purchased_product(auth.uid(), product_id)
  );

-- Create a view to show products a user has purchased but not yet reviewed
CREATE OR REPLACE VIEW user_purchasable_reviews AS
SELECT DISTINCT
  o.user_id,
  oi.product_id,
  p.name as product_name,
  o.id as order_id,
  o.created_at as purchase_date,
  CASE WHEN pr.id IS NULL THEN false ELSE true END as has_reviewed
FROM
  orders o
  JOIN order_items oi ON o.id = oi.order_id
  JOIN products p ON oi.product_id = p.id
  LEFT JOIN product_reviews pr ON pr.product_id = oi.product_id AND pr.user_id = o.user_id
WHERE
  o.status IN ('delivered', 'shipped');

-- Add comment to explain the function
COMMENT ON FUNCTION has_user_purchased_product IS 'Checks if a user has purchased a specific product';
