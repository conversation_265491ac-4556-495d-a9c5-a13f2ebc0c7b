/**
 * Real-time Orders Hook
 * 
 * This hook provides real-time updates for orders using Supabase subscriptions.
 * It's designed for admin dashboards to get live updates when orders change.
 */
import { useEffect, useState, useCallback } from 'react';
import { supabase } from '@/lib/supabase';
import { Order } from '@/services/orderService';
import { toast } from '@/hooks/use-toast';

interface UseRealtimeOrdersOptions {
  onOrderUpdate?: (order: Order) => void;
  onOrderInsert?: (order: Order) => void;
  onOrderDelete?: (orderId: string) => void;
  enabled?: boolean;
}

export function useRealtimeOrders(options: UseRealtimeOrdersOptions = {}) {
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  
  const {
    onOrderUpdate,
    onOrderInsert,
    onOrderDelete,
    enabled = true
  } = options;

  const handleOrderChange = useCallback((payload: any) => {
    const { eventType, new: newRecord, old: oldRecord } = payload;
    
    setLastUpdate(new Date());
    
    switch (eventType) {
      case 'INSERT':
        if (onOrderInsert && newRecord) {
          onOrderInsert(newRecord as Order);
          toast({
            title: 'New Order',
            description: `Order #${newRecord.id.substring(0, 8)} has been placed.`,
          });
        }
        break;
        
      case 'UPDATE':
        if (onOrderUpdate && newRecord) {
          onOrderUpdate(newRecord as Order);
          
          // Show notification for status changes
          if (oldRecord && oldRecord.status !== newRecord.status) {
            toast({
              title: 'Order Status Updated',
              description: `Order #${newRecord.id.substring(0, 8)} status changed to ${newRecord.status}.`,
            });
          }
          
          // Show notification for payment status changes
          if (oldRecord && oldRecord.payment_status !== newRecord.payment_status) {
            toast({
              title: 'Payment Status Updated',
              description: `Order #${newRecord.id.substring(0, 8)} payment status changed to ${newRecord.payment_status}.`,
            });
          }
        }
        break;
        
      case 'DELETE':
        if (onOrderDelete && oldRecord) {
          onOrderDelete(oldRecord.id);
          toast({
            title: 'Order Deleted',
            description: `Order #${oldRecord.id.substring(0, 8)} has been deleted.`,
            variant: 'destructive'
          });
        }
        break;
    }
  }, [onOrderUpdate, onOrderInsert, onOrderDelete]);

  useEffect(() => {
    if (!enabled) {
      setIsConnected(false);
      return;
    }

    // Subscribe to orders table changes
    const ordersSubscription = supabase
      .channel('orders-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'orders'
        },
        handleOrderChange
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          setIsConnected(true);
          console.log('Real-time orders subscription active');
        } else if (status === 'CHANNEL_ERROR') {
          setIsConnected(false);
          console.error('Real-time orders subscription error');
        } else if (status === 'CLOSED') {
          setIsConnected(false);
          console.log('Real-time orders subscription closed');
        }
      });

    // Subscribe to payment_transactions table changes for payment updates
    const paymentsSubscription = supabase
      .channel('payments-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'payment_transactions'
        },
        (payload) => {
          const { eventType, new: newRecord } = payload;
          
          if (eventType === 'UPDATE' && newRecord) {
            setLastUpdate(new Date());
            
            // Notify about payment status changes
            if (newRecord.status === 'completed') {
              toast({
                title: 'Payment Completed',
                description: `Payment for order has been completed.`,
              });
            } else if (newRecord.status === 'failed') {
              toast({
                title: 'Payment Failed',
                description: `Payment for order has failed.`,
                variant: 'destructive'
              });
            }
          }
        }
      )
      .subscribe();

    // Cleanup subscriptions on unmount
    return () => {
      ordersSubscription.unsubscribe();
      paymentsSubscription.unsubscribe();
      setIsConnected(false);
    };
  }, [enabled, handleOrderChange]);

  return {
    isConnected,
    lastUpdate
  };
}

/**
 * Hook for real-time order updates in admin dashboard
 */
export function useAdminRealtimeOrders(
  orders: Order[],
  setOrders: (orders: Order[]) => void
) {
  const handleOrderUpdate = useCallback((updatedOrder: Order) => {
    setOrders(orders.map(order => 
      order.id === updatedOrder.id ? updatedOrder : order
    ));
  }, [orders, setOrders]);

  const handleOrderInsert = useCallback((newOrder: Order) => {
    setOrders([newOrder, ...orders]);
  }, [orders, setOrders]);

  const handleOrderDelete = useCallback((orderId: string) => {
    setOrders(orders.filter(order => order.id !== orderId));
  }, [orders, setOrders]);

  return useRealtimeOrders({
    onOrderUpdate: handleOrderUpdate,
    onOrderInsert: handleOrderInsert,
    onOrderDelete: handleOrderDelete,
    enabled: true
  });
}
