import React, { useState, useRef } from 'react';
import { User, Upload, X, Loader2 } from 'lucide-react';
import { uploadProfilePicture } from '@/services/fileUploadService';
import { toast } from '@/hooks/use-toast';

interface AvatarUploadProps {
  userId: string;
  currentAvatarUrl?: string;
  onAvatarChange: (url: string | null) => void;
  size?: 'sm' | 'md' | 'lg';
}

const AvatarUpload: React.FC<AvatarUploadProps> = ({
  userId,
  currentAvatarUrl,
  onAvatarChange,
  size = 'md'
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Size classes based on the size prop
  const sizeClasses = {
    sm: 'w-16 h-16',
    md: 'w-24 h-24',
    lg: 'w-32 h-32'
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    try {
      const url = await uploadProfilePicture(userId, file);
      if (url) {
        onAvatarChange(url);
        toast({
          title: 'Profile picture updated',
          description: 'Your profile picture has been successfully updated'
        });
      } else {
        toast({
          title: 'Upload failed',
          description: 'Failed to upload profile picture. Please try again.',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error uploading profile picture:', error);
      toast({
        title: 'Upload failed',
        description: 'An error occurred while uploading your profile picture',
        variant: 'destructive'
      });
    } finally {
      setIsUploading(false);
      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemoveAvatar = () => {
    onAvatarChange(null);
  };

  return (
    <div className="flex flex-col items-center">
      <div className={`relative ${sizeClasses[size]} rounded-full overflow-hidden bg-badhees-100 mb-2`}>
        {currentAvatarUrl ? (
          <img
            src={currentAvatarUrl}
            alt="Profile"
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <User className="h-1/2 w-1/2 text-badhees-800" />
          </div>
        )}

        {isUploading && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <Loader2 className="h-8 w-8 text-white animate-spin" />
          </div>
        )}
      </div>

      <div className="flex space-x-1.5">
        <button
          type="button"
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
          className="text-xs px-2 py-1 bg-badhees-800 text-white rounded hover:bg-badhees-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Upload className="h-2.5 w-2.5 mr-1" />
          {currentAvatarUrl ? 'Change' : 'Upload'}
        </button>

        {currentAvatarUrl && (
          <button
            type="button"
            onClick={handleRemoveAvatar}
            disabled={isUploading}
            className="text-xs px-2 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <X className="h-2.5 w-2.5 mr-1" />
            Remove
          </button>
        )}
      </div>

      <label htmlFor="avatar-upload" className="sr-only">Upload profile picture</label>
      <input
        id="avatar-upload"
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        className="hidden"
        aria-label="Upload profile picture"
      />
    </div>
  );
};

export default AvatarUpload;
