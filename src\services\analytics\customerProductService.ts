/**
 * Customer and Product Analytics Service
 *
 * This module provides functions to fetch customer and product-related analytics data.
 */
import { supabase } from '@/lib/supabase';
import { handleSupabaseError } from '@/utils/supabaseHelpers';
import { CategoryPerformance, CustomerStats, TopProductsSortBy, TopSellingProduct } from './types';

/**
 * Get customer statistics (total, active, inactive)
 * @returns Customer statistics
 */
export const getCustomerStats = async (): Promise<CustomerStats> => {
  try {
    // Initialize default values
    let total = 0;
    let active = 0;
    let inactive = 0;
    let totalCustomers = 0;

    try {
      // Get total customers (users with role 'user')
      const { count, error } = await supabase
        .from('user_profiles')
        .select('*', { count: 'exact', head: true })
        .eq('role', 'user');

      if (!error) {
        total = count || 0;
      } else {
        console.error('Error fetching total users:', error.message);
      }
    } catch (err) {
      console.error('Exception fetching total users:', err);
    }

    // Note: Active/Inactive user tracking has been removed as it's no longer needed
    // Setting default values for backward compatibility
    active = 0;
    inactive = 0;

    try {
      // Get total customers (all users regardless of role)
      const { count, error } = await supabase
        .from('user_profiles')
        .select('*', { count: 'exact', head: true });

      if (!error) {
        totalCustomers = count || 0;
      } else {
        console.error('Error fetching all users:', error.message);
      }
    } catch (err) {
      console.error('Exception fetching all users:', err);
    }

    return {
      total,
      active,
      inactive,
      totalCustomers
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error in getCustomerStats:', errorMessage);
    handleSupabaseError(error, 'getCustomerStats', false);
    return {
      total: 0,
      active: 0,
      inactive: 0,
      totalCustomers: 0
    };
  }
};

/**
 * Get category performance (sales by category)
 * @returns Category performance data
 */
export const getCategoryPerformance = async (): Promise<CategoryPerformance[]> => {
  try {
    const { data, error } = await supabase.rpc('get_sales_by_category');

    if (error) {
      console.error('Error fetching category performance:', error);
      return [];
    }

    return data.map((item: any) => ({
      name: item.category_name,
      value: parseFloat(String(item.total_sales))
    }));
  } catch (error) {
    handleSupabaseError(error, 'getCategoryPerformance', false);
    return [];
  }
};

/**
 * Get top selling products by revenue or quantity
 * @param limit Number of products to return
 * @param sortBy Sort by revenue or quantity
 * @returns Top selling products data
 */
export const getTopSellingProducts = async (
  limit: number = 5,
  sortBy: TopProductsSortBy = 'revenue'
): Promise<TopSellingProduct[]> => {
  try {
    // Choose the appropriate RPC function based on sortBy parameter
    const rpcFunction = sortBy === 'revenue' ? 'get_top_selling_products' : 'get_top_selling_products_by_quantity';

    const { data, error } = await supabase.rpc(rpcFunction, { limit_count: limit });

    if (error) {
      console.error(`Error fetching top selling products by ${sortBy}:`, error);
      return [];
    }

    // Get product images
    const productIds = data.map((item: any) => item.product_id);
    const { data: imageData, error: imageError } = await supabase
      .from('product_images')
      .select('product_id, image_url')
      .in('product_id', productIds)
      .eq('is_primary', true);

    if (imageError) {
      console.error('Error fetching product images:', imageError);
    }

    // Create a map of product ID to image URL
    const imageMap = new Map();
    if (imageData) {
      imageData.forEach((item: any) => {
        imageMap.set(item.product_id, item.image_url);
      });
    }

    return data.map((item: any) => ({
      id: item.product_id,
      name: item.product_name,
      category: item.category_name,
      total_sales: parseFloat(String(item.total_sales || 0)),
      quantity_sold: parseInt(String(item.quantity_sold || 0)),
      image_url: imageMap.get(item.product_id) || 'https://placehold.co/100x100?text=No+Image'
    }));
  } catch (error) {
    handleSupabaseError(error, `getTopSellingProducts (${sortBy})`, false);
    return [];
  }
};

/**
 * Get total products count
 * @returns Total products count
 */
export const getTotalProductsCount = async (): Promise<number> => {
  try {
    const { count, error } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .neq('status', 'deleted');

    if (error) {
      console.error('Error fetching total products count:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    handleSupabaseError(error, 'getTotalProductsCount', false);
    return 0;
  }
};

/**
 * Get total projects count
 * @returns Total projects count
 */
export const getTotalProjectsCount = async (): Promise<number> => {
  try {
    // Count completed projects
    const { count: completedCount, error: completedError } = await supabase
      .from('completed_projects')
      .select('*', { count: 'exact', head: true })
      .neq('status', 'deleted');

    if (completedError) {
      console.error('Error fetching completed projects count:', completedError);
      return 0;
    }

    // Count custom projects
    const { count: customCount, error: customError } = await supabase
      .from('custom_projects')
      .select('*', { count: 'exact', head: true })
      .neq('status', 'deleted');

    if (customError) {
      console.error('Error fetching custom projects count:', customError);
      return 0;
    }

    return (completedCount || 0) + (customCount || 0);
  } catch (error) {
    handleSupabaseError(error, 'getTotalProjectsCount', false);
    return 0;
  }
};
