
import React, { useEffect, useRef } from "react";
import { ArrowRight } from "lucide-react";
import { Link } from "react-router-dom";

const Hero = () => {
  const heroRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const parallaxEffect = (e: MouseEvent) => {
      const mouseX = e.clientX / window.innerWidth - 0.5;
      const mouseY = e.clientY / window.innerHeight - 0.5;

      if (heroRef.current) {
        const elements = heroRef.current.querySelectorAll('.parallax');

        elements.forEach((el) => {
          const element = el as HTMLElement;
          const speedX = Number(element.dataset.speedX || 20);
          const speedY = Number(element.dataset.speedY || 20);

          const moveX = mouseX * speedX;
          const moveY = mouseY * speedY;

          element.style.transform = `translate(${moveX}px, ${moveY}px)`;
        });
      }
    };

    document.addEventListener('mousemove', parallaxEffect);

    return () => {
      document.removeEventListener('mousemove', parallaxEffect);
    };
  }, []);

  return (
    <div ref={heroRef} className="hero-section">
      {/* Background elements */}
      <div className="absolute inset-0 bg-badhees-50 overflow-hidden">
        <div className="absolute -top-20 -right-20 w-96 h-96 rounded-full bg-badhees-100 mix-blend-multiply opacity-70 parallax" data-speed-x="10" data-speed-y="15"></div>
        <div className="absolute top-1/3 -left-20 w-72 h-72 rounded-full bg-badhees-accent/10 blur-2xl parallax" data-speed-x="25" data-speed-y="10"></div>
        <div className="absolute bottom-20 right-1/4 w-64 h-64 rounded-full bg-badhees-accent/20 blur-3xl parallax" data-speed-x="15" data-speed-y="20"></div>
      </div>

      {/* Content */}
      <div className="section-container relative z-10 flex flex-col md:flex-row items-center">
        <div className="md:w-1/2 space-y-6 md:pr-10">
          <div>
            <span className="inline-block px-3 py-1 mb-4 text-xs font-medium tracking-wider text-badhees-accent bg-badhees-accent/10 rounded-full uppercase animate-fadeIn">
              Handcrafted Luxury
            </span>
          </div>
          <h1 className="heading-1 text-badhees-800 animate-slideUp">
            Timeless Design For Modern Living
          </h1>
          <p className="subtitle animate-slideUp animate-delay-100">
            Discover furniture that transcends trends, combining impeccable craftsmanship with minimalist elegance for spaces that inspire.
          </p>
          <div className="flex flex-wrap gap-4 pt-4 animate-slideUp animate-delay-200">
            <Link
              to="/products"
              className="btn-primary"
            >
              Explore Products
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
            <Link to="/about" className="btn-secondary">
              Our Story
            </Link>
          </div>
        </div>

        <div className="mt-10 md:mt-0 md:w-1/2 animate-fadeIn animate-delay-300">
          <div className="relative">
            {/* Main Image */}
            <div className="rounded-2xl overflow-hidden shadow-2xl transition-all duration-500 hover:shadow-3xl">
              <img
                src="/images/hero/heroban1.jpg"
                alt="Modern living room with minimalist furniture"
                className="w-full h-auto object-cover"
                loading="eager"
              />
            </div>

            {/* Floating elements */}
            <div className="absolute -bottom-6 -left-6 w-48 p-4 glass-morphism rounded-lg shadow-lg parallax animate-slideUp animate-delay-400" data-speed-x="10" data-speed-y="10">
              <div className="flex items-center space-x-3">
                <div className="bg-badhees-accent/20 rounded-full p-2">
                  <svg className="w-4 h-4 text-badhees-accent" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                  </svg>
                </div>
                <div>
                  <p className="text-xs font-medium text-badhees-700">Handcrafted</p>
                  <p className="text-xs text-badhees-500">With passion</p>
                </div>
              </div>
            </div>

            <div className="absolute -top-6 -right-6 w-48 p-4 glass-morphism rounded-lg shadow-lg parallax animate-slideUp animate-delay-500" data-speed-x="15" data-speed-y="15">
              <div className="flex items-center space-x-3">
                <div className="bg-badhees-accent/20 rounded-full p-2">
                  <svg className="w-4 h-4 text-badhees-accent" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                  </svg>
                </div>
                <div>
                  <p className="text-xs font-medium text-badhees-700">10 Year</p>
                  <p className="text-xs text-badhees-500">Warranty</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;
