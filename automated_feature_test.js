/**
 * Automated Feature Testing Script
 * Run this in browser console to test recent features
 */

class FeatureTester {
  constructor() {
    this.results = {
      whatsapp: { status: 'pending', details: [] },
      payment: { status: 'pending', details: [] },
      admin: { status: 'pending', details: [] },
      reviews: { status: 'pending', details: [] },
      ui: { status: 'pending', details: [] }
    };
  }

  log(category, message, status = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] ${status.toUpperCase()}: ${message}`;
    console.log(logEntry);
    this.results[category].details.push({ message, status, timestamp });
  }

  // Test WhatsApp Integration
  async testWhatsAppIntegration() {
    this.log('whatsapp', 'Testing WhatsApp integration...', 'info');
    
    try {
      // Check floating WhatsApp button
      const floatingButton = document.querySelector('[data-testid="floating-whatsapp"], .floating-whatsapp, [href*="wa.me"]');
      if (floatingButton) {
        this.log('whatsapp', '✅ Floating WhatsApp button found', 'success');
        
        // Check if it has the correct business number
        const href = floatingButton.href || floatingButton.getAttribute('href');
        if (href && href.includes('8197705438')) {
          this.log('whatsapp', '✅ Correct business number (8197705438) found', 'success');
        } else {
          this.log('whatsapp', '❌ Business number not found or incorrect', 'error');
        }
      } else {
        this.log('whatsapp', '❌ Floating WhatsApp button not found', 'error');
      }

      // Check footer WhatsApp links
      const footerWhatsApp = document.querySelector('footer [href*="wa.me"], footer [href*="whatsapp"]');
      if (footerWhatsApp) {
        this.log('whatsapp', '✅ Footer WhatsApp link found', 'success');
      } else {
        this.log('whatsapp', '❌ Footer WhatsApp link not found', 'error');
      }

      this.results.whatsapp.status = 'completed';
    } catch (error) {
      this.log('whatsapp', `❌ Error testing WhatsApp: ${error.message}`, 'error');
      this.results.whatsapp.status = 'error';
    }
  }

  // Test UI Components
  async testUIComponents() {
    this.log('ui', 'Testing UI components...', 'info');
    
    try {
      // Check for React errors in console
      const reactErrors = window.__REACT_DEVTOOLS_GLOBAL_HOOK__?.reactDevtoolsAgent?.errors || [];
      if (reactErrors.length === 0) {
        this.log('ui', '✅ No React errors detected', 'success');
      } else {
        this.log('ui', `❌ ${reactErrors.length} React errors found`, 'error');
      }

      // Check for console errors
      const originalError = console.error;
      let errorCount = 0;
      console.error = (...args) => {
        errorCount++;
        originalError.apply(console, args);
      };

      // Wait a bit to catch any errors
      setTimeout(() => {
        console.error = originalError;
        if (errorCount === 0) {
          this.log('ui', '✅ No console errors detected', 'success');
        } else {
          this.log('ui', `❌ ${errorCount} console errors detected`, 'error');
        }
      }, 2000);

      // Check responsive design
      const isMobile = window.innerWidth < 768;
      this.log('ui', `📱 Testing on ${isMobile ? 'mobile' : 'desktop'} viewport`, 'info');

      // Check for loading states
      const loadingElements = document.querySelectorAll('[data-loading="true"], .loading, .spinner');
      this.log('ui', `🔄 Found ${loadingElements.length} loading elements`, 'info');

      this.results.ui.status = 'completed';
    } catch (error) {
      this.log('ui', `❌ Error testing UI: ${error.message}`, 'error');
      this.results.ui.status = 'error';
    }
  }

  // Test Payment System (frontend checks)
  async testPaymentSystem() {
    this.log('payment', 'Testing payment system frontend...', 'info');
    
    try {
      // Check if Razorpay script is loaded
      if (window.Razorpay) {
        this.log('payment', '✅ Razorpay script loaded', 'success');
      } else {
        this.log('payment', '❌ Razorpay script not loaded', 'error');
      }

      // Check for payment method options
      const paymentMethods = document.querySelectorAll('[data-payment-method], input[name*="payment"]');
      if (paymentMethods.length > 0) {
        this.log('payment', `✅ Found ${paymentMethods.length} payment method options`, 'success');
      } else {
        this.log('payment', '❌ No payment method options found', 'error');
      }

      // Check for payment forms
      const paymentForms = document.querySelectorAll('form[data-payment], form[action*="payment"]');
      this.log('payment', `📋 Found ${paymentForms.length} payment forms`, 'info');

      this.results.payment.status = 'completed';
    } catch (error) {
      this.log('payment', `❌ Error testing payment: ${error.message}`, 'error');
      this.results.payment.status = 'error';
    }
  }

  // Test Reviews System
  async testReviewsSystem() {
    this.log('reviews', 'Testing reviews system...', 'info');
    
    try {
      // Check for rating displays
      const ratingElements = document.querySelectorAll('[data-rating], .rating, .stars');
      if (ratingElements.length > 0) {
        this.log('reviews', `✅ Found ${ratingElements.length} rating displays`, 'success');
      } else {
        this.log('reviews', '❌ No rating displays found', 'error');
      }

      // Check for review forms
      const reviewForms = document.querySelectorAll('form[data-review], [data-testid*="review"]');
      this.log('reviews', `📝 Found ${reviewForms.length} review forms`, 'info');

      // Check for review modals
      const reviewModals = document.querySelectorAll('[data-modal*="review"], .review-modal');
      this.log('reviews', `🔲 Found ${reviewModals.length} review modals`, 'info');

      this.results.reviews.status = 'completed';
    } catch (error) {
      this.log('reviews', `❌ Error testing reviews: ${error.message}`, 'error');
      this.results.reviews.status = 'error';
    }
  }

  // Test Admin Features (if accessible)
  async testAdminFeatures() {
    this.log('admin', 'Testing admin features...', 'info');
    
    try {
      // Check if we're on admin page
      const isAdminPage = window.location.pathname.includes('/admin');
      if (isAdminPage) {
        this.log('admin', '✅ On admin page', 'success');
        
        // Check for admin navigation
        const adminNav = document.querySelector('[data-admin-nav], .admin-sidebar, nav[data-testid*="admin"]');
        if (adminNav) {
          this.log('admin', '✅ Admin navigation found', 'success');
        }

        // Check for orders table
        const ordersTable = document.querySelector('table[data-orders], [data-testid*="orders-table"]');
        if (ordersTable) {
          this.log('admin', '✅ Orders table found', 'success');
        }

        // Check for payment status badges
        const paymentBadges = document.querySelectorAll('[data-payment-status], .payment-status');
        this.log('admin', `💳 Found ${paymentBadges.length} payment status badges`, 'info');

      } else {
        this.log('admin', '📝 Not on admin page - navigate to /admin to test admin features', 'info');
      }

      this.results.admin.status = 'completed';
    } catch (error) {
      this.log('admin', `❌ Error testing admin: ${error.message}`, 'error');
      this.results.admin.status = 'error';
    }
  }

  // Run all tests
  async runAllTests() {
    console.log('🚀 Starting comprehensive feature testing...');
    console.log('='.repeat(50));
    
    await this.testUIComponents();
    await this.testWhatsAppIntegration();
    await this.testPaymentSystem();
    await this.testReviewsSystem();
    await this.testAdminFeatures();
    
    console.log('='.repeat(50));
    console.log('📊 TEST RESULTS SUMMARY:');
    console.log('='.repeat(50));
    
    Object.entries(this.results).forEach(([category, result]) => {
      const status = result.status === 'completed' ? '✅' : 
                    result.status === 'error' ? '❌' : '⏳';
      console.log(`${status} ${category.toUpperCase()}: ${result.status}`);
      
      if (result.details.length > 0) {
        result.details.forEach(detail => {
          const icon = detail.status === 'success' ? '  ✅' : 
                      detail.status === 'error' ? '  ❌' : '  ℹ️';
          console.log(`${icon} ${detail.message}`);
        });
      }
      console.log('');
    });
    
    return this.results;
  }
}

// Auto-run the tests
const tester = new FeatureTester();
tester.runAllTests().then(results => {
  console.log('🎯 Testing completed! Results stored in window.testResults');
  window.testResults = results;
});

// Make tester available globally for manual testing
window.featureTester = tester;
