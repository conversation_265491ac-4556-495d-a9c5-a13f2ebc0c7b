-- Create user_addresses table to store multiple addresses per user
CREATE TABLE IF NOT EXISTS user_addresses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  street TEXT NOT NULL,
  city TEXT NOT NULL,
  state TEXT NOT NULL,
  postal_code TEXT NOT NULL,
  country TEXT NOT NULL,
  phone TEXT,
  is_default_shipping BOOLEAN DEFAULT FALSE,
  is_default_billing BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Set up Row Level Security (RLS)
ALTER TABLE user_addresses ENABLE ROW LEVEL SECURITY;

-- Create policies for user_addresses
-- Users can view their own addresses
CREATE POLICY "Users can view their own addresses" 
  ON user_addresses FOR SELECT 
  USING (auth.uid() = user_id);

-- Users can insert their own addresses
CREATE POLICY "Users can insert their own addresses" 
  ON user_addresses FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Users can update their own addresses
CREATE POLICY "Users can update their own addresses" 
  ON user_addresses FOR UPDATE 
  USING (auth.uid() = user_id);

-- Users can delete their own addresses
CREATE POLICY "Users can delete their own addresses" 
  ON user_addresses FOR DELETE 
  USING (auth.uid() = user_id);

-- Admins can view all addresses
CREATE POLICY "Admins can view all addresses" 
  ON user_addresses FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Create a function to ensure only one default shipping address per user
CREATE OR REPLACE FUNCTION ensure_single_default_shipping()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.is_default_shipping = TRUE THEN
    UPDATE user_addresses
    SET is_default_shipping = FALSE
    WHERE user_id = NEW.user_id
    AND id != NEW.id
    AND is_default_shipping = TRUE;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to enforce single default shipping address
CREATE TRIGGER ensure_single_default_shipping_trigger
BEFORE INSERT OR UPDATE ON user_addresses
FOR EACH ROW
EXECUTE FUNCTION ensure_single_default_shipping();

-- Create a function to ensure only one default billing address per user
CREATE OR REPLACE FUNCTION ensure_single_default_billing()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.is_default_billing = TRUE THEN
    UPDATE user_addresses
    SET is_default_billing = FALSE
    WHERE user_id = NEW.user_id
    AND id != NEW.id
    AND is_default_billing = TRUE;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to enforce single default billing address
CREATE TRIGGER ensure_single_default_billing_trigger
BEFORE INSERT OR UPDATE ON user_addresses
FOR EACH ROW
EXECUTE FUNCTION ensure_single_default_billing();

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_user_addresses_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update the updated_at column
CREATE TRIGGER update_user_addresses_updated_at
BEFORE UPDATE ON user_addresses
FOR EACH ROW
EXECUTE FUNCTION update_user_addresses_updated_at();
