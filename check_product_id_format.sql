-- Check the actual format of product IDs in your database
-- This will help us understand if they are UUIDs or integers

-- Check the products table structure
SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'products' 
  AND column_name = 'id';

-- Check some sample product IDs
SELECT 
  id,
  name,
  pg_typeof(id) as id_type
FROM products 
LIMIT 10;

-- Check if product_reviews table expects UUID or integer
SELECT 
  column_name,
  data_type,
  is_nullable
FROM information_schema.columns 
WHERE table_name = 'product_reviews' 
  AND column_name IN ('id', 'product_id');

-- Check some sample product_reviews if any exist
SELECT 
  id,
  product_id,
  pg_typeof(product_id) as product_id_type
FROM product_reviews 
LIMIT 5;
