import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useLocation, useNavigate, Link } from "react-router-dom";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import ProductCard from "@/components/products/ProductCard";
import EnhancedProductCard from "@/components/products/EnhancedProductCard";
import { Sliders, ChevronDown, X, Search, Loader2 } from "lucide-react";
import '@/styles/animations.css';
import './products.css';
import './mobile-optimizations.css';
import { Button } from "@/components/ui/button-system";
import { SearchInput } from "@/components/search";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { useProducts, useProductSearch, useFilteredProducts } from "@/hooks/useProducts";
import { useQueryClient } from "@tanstack/react-query";
import { PullToRefresh } from "@/components/ui/pull-to-refresh";
import { useTabVisibility } from "@/hooks/use-optimized-render";

const Products = () => {
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [sortOption, setSortOption] = useState("featured");
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // Handle tab visibility changes - refetch data when tab becomes visible
  const handleTabVisible = useCallback(() => {
    console.log('Tab became visible - refreshing products data');
    queryClient.invalidateQueries({ queryKey: ['products'] });
  }, [queryClient]);

  useTabVisibility(handleTabVisible);

  // Fetch all products using React Query
  const { data: allProducts = [], isLoading: isLoadingProducts, refetch: refetchProducts } = useProducts();

  // Fetch search results if we have a search query
  const { data: searchResults = [], isLoading: isLoadingSearch, refetch: refetchSearch } =
    useProductSearch(searchQuery);

  // Determine if we're in a loading state
  const isLoading = isLoadingProducts || isLoadingSearch || isRefreshing;

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const category = params.get("category");
    const search = params.get("search");

    if (category) {
      setSelectedCategory(category);
      console.log('Category from URL:', category);
    } else {
      setSelectedCategory(null);
    }

    if (search) {
      setSearchQuery(search);
    }

    window.scrollTo(0, 0);
  }, [location.search]);

  const categories = [
    "All",
    "Living Room Furniture",
    "Bedroom Furniture",
    "Dining Room Furniture",
    "Home Office Furniture",
    "Storage Solutions",
    "Kids & Nursery Furniture",
    "Vibe & Decor",
    "Custom & Specialty Furniture"
  ];

  const handleCategorySelect = useCallback((category: string) => {
    // Special case for Custom & Specialty - redirect to custom interiors page
    if (category === "Custom & Specialty Furniture") {
      navigate("/custom-interiors");
      return;
    }

    setSelectedCategory(category === "All" ? null : category);

    const params = new URLSearchParams(location.search);
    if (category === "All") {
      params.delete("category");
    } else {
      params.set("category", category);
    }

    // Clear any existing filters when changing categories
    params.delete("sort");

    navigate(`/products?${params.toString()}`);
  }, [navigate, location.search]);

  const handleSearch = useCallback(async (query: string) => {
    if (!query.trim()) return;

    // Search across ALL categories - don't keep category filter
    const params = new URLSearchParams();
    params.set("search", query.trim());
    navigate(`/products?${params.toString()}`);

    // The search will be performed automatically by the useProductSearch hook
    setIsSearching(true);

    // Invalidate the search query to force a refetch
    queryClient.invalidateQueries({ queryKey: ['products', 'search', query.trim()] });

    // Set isSearching to false after a short delay to show the loading state
    setTimeout(() => {
      setIsSearching(false);
    }, 500);
  }, [navigate, queryClient]);

  const clearFilters = useCallback(() => {
    setSelectedCategory(null);
    setSortOption("featured");
    setSearchQuery("");
    navigate("/products");

    // Invalidate the products query to force a refetch
    queryClient.invalidateQueries({ queryKey: ['products', 'list'] });
  }, [navigate, queryClient]);

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    try {
      // Invalidate all product-related queries
      await queryClient.invalidateQueries({ queryKey: ['products'] });

      // Refetch current data
      if (searchQuery) {
        await refetchSearch();
      } else {
        await refetchProducts();
      }
    } catch (error) {
      console.error('Refresh failed:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [queryClient, searchQuery, refetchSearch, refetchProducts]);

  // Create a memoized category matcher function
  const matchesCategory = useCallback((productCategory: string, selectedCat: string) => {
    if (!selectedCat) return true;

    // Normalize category names for comparison
    const normalizedProductCategory = productCategory?.toLowerCase() || '';
    const normalizedSelectedCat = selectedCat.toLowerCase();
    const selectedCatNoFurniture = normalizedSelectedCat.replace(' furniture', '');

    // Exact match
    if (normalizedProductCategory === normalizedSelectedCat) return true;

    // Check if product category is part of selected category
    if (normalizedSelectedCat.includes(normalizedProductCategory)) return true;

    // Check if selected category without 'furniture' matches product category
    if (normalizedProductCategory.includes(selectedCatNoFurniture)) return true;

    // Special case for 'Living Room' vs 'Living Room Furniture'
    if (normalizedSelectedCat === 'living room furniture' && normalizedProductCategory === 'living room') return true;
    if (normalizedSelectedCat === 'bedroom furniture' && normalizedProductCategory === 'bedroom') return true;
    if (normalizedSelectedCat === 'dining room furniture' && normalizedProductCategory === 'dining room') return true;
    if (normalizedSelectedCat === 'home office furniture' && normalizedProductCategory === 'home office') return true;
    if (normalizedSelectedCat === 'storage solutions' && normalizedProductCategory === 'storage') return true;
    if (normalizedSelectedCat === 'kids & nursery furniture' &&
        (normalizedProductCategory === 'kids' || normalizedProductCategory === 'nursery' || normalizedProductCategory === 'kids & nursery')) return true;

    // Special case for the new "Vibe & Decor" category
    if (normalizedSelectedCat === 'vibe & decor' &&
        (normalizedProductCategory === 'outdoor' ||
         normalizedProductCategory === 'outdoor furniture' ||
         normalizedProductCategory === 'accent' ||
         normalizedProductCategory === 'accent furniture' ||
         normalizedProductCategory === 'decorative' ||
         normalizedProductCategory === 'decorative accessories')) return true;

    return false;
  }, []);

  // Memoized filtered products
  const filteredProducts = useMemo(() => {
    // If we have search results from server-side search, use those directly (no category filter)
    if (searchQuery && searchResults.length > 0) {
      return searchResults.filter(product => product.status === 'active');
    }

    // Otherwise, apply category filter to all products
    return allProducts.filter((product) => {
      const productMatchesCategory = selectedCategory ?
        matchesCategory(product.category, selectedCategory) : true;
      const isActive = product.status === 'active';

      return productMatchesCategory && isActive;
    });
  }, [allProducts, searchResults, searchQuery, selectedCategory, matchesCategory]);

  // Memoized sorted products
  const sortedProducts = useMemo(() => {
    return [...filteredProducts].sort((a, b) => {
      switch (sortOption) {
        case "price-low-high":
          return a.price - b.price;
        case "price-high-low":
          return b.price - a.price;
        case "newest":
          return a.isNew ? -1 : b.isNew ? 1 : 0;
        case "sale":
          return a.isSale ? -1 : b.isSale ? 1 : 0;
        default:
          return 0;
      }
    });
  }, [filteredProducts, sortOption]);

  return (
    <div className="min-h-screen">
      <Navbar />

      <PullToRefresh onRefresh={handleRefresh} className="min-h-screen">
        <div className="pt-20 md:pt-28 pb-4 md:pb-16 bg-badhees-50">
        <div className="max-w-[1400px] mx-auto px-4 sm:px-8">
          <Breadcrumb className="mb-2 md:mb-6 breadcrumb-mobile">
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/">Home</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/shop">Shop</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              {selectedCategory && (
                <>
                  <BreadcrumbItem>
                    <BreadcrumbPage>{selectedCategory}</BreadcrumbPage>
                  </BreadcrumbItem>
                </>
              )}
              {!selectedCategory && (
                <BreadcrumbItem>
                  <BreadcrumbPage>All Products</BreadcrumbPage>
                </BreadcrumbItem>
              )}
            </BreadcrumbList>
          </Breadcrumb>

          <div className="text-center max-w-2xl mx-auto mb-2 md:mb-8">
            <h1 className="text-2xl md:text-4xl font-bold text-badhees-800 mb-1 md:mb-4 heading-mobile">
              {selectedCategory || "All Products"}
            </h1>
            <p className="text-sm md:text-lg text-badhees-600 description-mobile">
              Explore our collection of meticulously crafted furniture pieces designed to transform your living spaces.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-[1400px] mx-auto px-4 sm:px-8 py-6 md:py-12 products-main-container">
        <div className="flex justify-between items-center mb-4 lg:hidden filter-sort-section">
          <Button
            type="button"
            onClick={() => setShowFilters(!showFilters)}
            variant="outline"
            size="sm"
            leftIcon={<Sliders className="h-4 w-4" />}
            aria-label="Show filters"
          >
            Filters
          </Button>

          <div className="relative">
            <label htmlFor="mobile-sort-select" className="sr-only">Sort products</label>
            <select
              id="mobile-sort-select"
              value={sortOption}
              onChange={(e) => setSortOption(e.target.value)}
              className="appearance-none px-4 py-2 pr-8 rounded border border-badhees-200 text-badhees-800 focus:outline-none focus:ring-1 focus:ring-badhees-accent"
              aria-label="Sort products"
            >
              <option value="featured">Featured</option>
              <option value="price-low-high">Price: Low to High</option>
              <option value="price-high-low">Price: High to Low</option>
              <option value="newest">New Arrivals</option>
              <option value="sale">On Sale</option>
            </select>
            <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-badhees-500 pointer-events-none" />
          </div>
        </div>

        <div className="mb-2 md:mb-8">
          <div className="max-w-md mx-auto lg:mx-0">
            <SearchInput
              value={searchQuery}
              onChange={setSearchQuery}
              onSearch={handleSearch}
              isLoading={isSearching}
              placeholder="Search products..."
              className="w-full search-input-mobile"
              showSearchButton={false}
            />
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {showFilters && (
            <div className="fixed inset-0 z-50 bg-white lg:hidden p-6 overflow-y-auto animate-fadeIn">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-medium">Filters</h2>
                <button
                  type="button"
                  onClick={() => setShowFilters(false)}
                  aria-label="Close filters"
                >
                  <X className="h-6 w-6 text-badhees-600" />
                </button>
              </div>

              <div className="space-y-8">
                <div>
                  <h3 className="text-sm font-medium text-badhees-800 mb-3">Category</h3>
                  <div className="space-y-2">
                    {categories.map((category) => (
                      <button
                        type="button"
                        key={category}
                        onClick={() => {
                          handleCategorySelect(category);
                          setShowFilters(false);
                        }}
                        className={`w-full text-left px-3 py-2 text-sm rounded transition-colors ${
                          (category === "All" && !selectedCategory) || selectedCategory === category
                            ? "bg-badhees-800 text-white"
                            : "text-badhees-700 hover:bg-badhees-100"
                        }`}
                      >
                        {category}
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <label htmlFor="filter-sort-select" className="text-sm font-medium text-badhees-800 mb-3 block">Sort By</label>
                  <select
                    id="filter-sort-select"
                    value={sortOption}
                    onChange={(e) => setSortOption(e.target.value)}
                    className="w-full px-3 py-2 border border-badhees-200 rounded appearance-none focus:outline-none focus:ring-1 focus:ring-badhees-accent"
                  >
                    <option value="featured">Featured</option>
                    <option value="price-low-high">Price: Low to High</option>
                    <option value="price-high-low">Price: High to Low</option>
                    <option value="newest">New Arrivals</option>
                    <option value="sale">On Sale</option>
                  </select>
                </div>

                <Button
                  type="button"
                  variant="outline"
                  size="mobile-friendly"
                  fullWidth
                  onClick={() => {
                    clearFilters();
                    setShowFilters(false);
                  }}
                >
                  Clear All Filters
                </Button>
              </div>
            </div>
          )}

          <aside className="hidden lg:block w-64 flex-shrink-0">
            <div className="sticky top-28 space-y-8">
              <div>
                <h3 className="text-sm font-medium text-badhees-800 mb-3">Category</h3>
                <div className="space-y-2">
                  {categories.map((category) => (
                    <button
                      type="button"
                      key={category}
                      onClick={() => handleCategorySelect(category)}
                      className={`w-full text-left px-3 py-2 text-sm rounded transition-colors ${
                        (category === "All" && !selectedCategory) || selectedCategory === category
                          ? "bg-badhees-800 text-white"
                          : "text-badhees-700 hover:bg-badhees-100"
                      }`}
                    >
                      {category}
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label htmlFor="sidebar-sort-select" className="text-sm font-medium text-badhees-800 mb-3 block">Sort By</label>
                <select
                  id="sidebar-sort-select"
                  value={sortOption}
                  onChange={(e) => setSortOption(e.target.value)}
                  className="w-full px-3 py-2 border border-badhees-200 rounded appearance-none focus:outline-none focus:ring-1 focus:ring-badhees-accent"
                >
                  <option value="featured">Featured</option>
                  <option value="price-low-high">Price: Low to High</option>
                  <option value="price-high-low">Price: High to Low</option>
                  <option value="newest">New Arrivals</option>
                  <option value="sale">On Sale</option>
                </select>
              </div>

              <Button
                type="button"
                variant="outline"
                size="sm"
                fullWidth
                onClick={clearFilters}
              >
                Clear All Filters
              </Button>
            </div>
          </aside>

          <div className="flex-1">
            {isLoading ? (
              <div className="flex justify-center items-center py-20">
                <Loader2 className="h-8 w-8 animate-spin text-badhees-accent" />
                <span className="ml-2 text-badhees-600">Loading products...</span>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 sm:gap-x-6 gap-y-6 sm:gap-y-10 product-grid-mobile">
                  {sortedProducts.map((product, index) => (
                    <div
                      key={product.id}
                      className={`animate-fadeIn product-item-delay-${Math.min(Math.floor(index * 50), 1000)}`}
                    >
                      <EnhancedProductCard
                        product={product}
                      />
                    </div>
                  ))}
                </div>

                {sortedProducts.length === 0 && (
                  <div className="text-center py-12">
                    <p className="text-badhees-500">No products found matching your criteria.</p>
                    <Button
                      type="button"
                      variant="link"
                      onClick={clearFilters}
                      className="mt-4"
                    >
                      Reset filters
                    </Button>

                    {/* Debug information - only visible in development */}
                    {import.meta.env.DEV && (
                      <div className="mt-8 p-4 border border-gray-300 rounded text-left max-w-lg mx-auto">
                        <h3 className="font-bold mb-2">Debug Information:</h3>
                        <p><strong>Selected Category:</strong> {selectedCategory || 'None'}</p>
                        <p><strong>Total Products:</strong> {allProducts.length}</p>
                        <p><strong>Available Categories:</strong> {[...new Set(allProducts.map(p => p.category))].join(', ')}</p>
                        <p><strong>Matching Products:</strong> {allProducts.filter(p => {
                          const productCategory = p.category?.toLowerCase() || '';
                          const selectedCat = selectedCategory?.toLowerCase() || '';
                          const selectedCatNoFurniture = selectedCat.replace(' furniture', '');

                          // Exact match
                          if (productCategory === selectedCat) return true;

                          // Check if product category is part of selected category
                          if (selectedCat.includes(productCategory)) return true;

                          // Check if selected category without 'furniture' matches product category
                          if (productCategory.includes(selectedCatNoFurniture)) return true;

                          // Special case for 'Living Room' vs 'Living Room Furniture'
                          if (selectedCat === 'living room furniture' && productCategory === 'living room') return true;
                          if (selectedCat === 'bedroom furniture' && productCategory === 'bedroom') return true;
                          if (selectedCat === 'dining room furniture' && productCategory === 'dining room') return true;
                          if (selectedCat === 'home office furniture' && productCategory === 'home office') return true;
                          if (selectedCat === 'storage solutions' && productCategory === 'storage') return true;
                          if (selectedCat === 'kids & nursery furniture' && (productCategory === 'kids' || productCategory === 'nursery' || productCategory === 'kids & nursery')) return true;

                          // Special case for the new "Vibe & Decor" category
                          if (selectedCat === 'vibe & decor' &&
                              (productCategory === 'outdoor' ||
                               productCategory === 'outdoor furniture' ||
                               productCategory === 'accent' ||
                               productCategory === 'accent furniture' ||
                               productCategory === 'decorative' ||
                               productCategory === 'decorative accessories')) return true;

                          return false;
                        }).length}</p>
                        <p><strong>Selected Category (normalized):</strong> {selectedCategory?.toLowerCase()?.replace(' furniture', '')}</p>
                      </div>
                    )}
                  </div>
                )}
              </>
            )}
          </div>
        </div>
        </div>

        <Footer />
      </PullToRefresh>
    </div>
  );
};

export default Products;
