import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';
import { toast } from '@/hooks/use-toast';
import { supabase } from '@/lib/supabase';
import { Session, User as SupabaseUser, AuthError } from '@supabase/supabase-js';
import { sendWelcomeEmail } from '@/services/emailService';

interface UserProfile {
  id: string;
  display_name?: string;
  role?: 'user' | 'admin';
}

interface User {
  id: string;
  email: string;
  name: string;
  displayName?: string;
  role?: 'user' | 'admin';
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  loginWithGoogle: () => Promise<boolean>;
  register: (name: string, email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  isAdmin: () => boolean;
  updateUserProfile?: (updatedData: Partial<User>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [session, setSession] = useState<Session | null>(null);

  // Initialize the auth state
  useEffect(() => {
    let isMounted = true;
    const initializeAuth = async () => {
      if (!isMounted) return;
      setIsLoading(true);

      // Clean up any old localStorage auth data from the previous auth system
      if (localStorage.getItem('user')) {
        console.log('Removing old auth data from localStorage');
        localStorage.removeItem('user');
      }

      try {
        // Get the current session
        const { data: { session } } = await supabase.auth.getSession();
        if (!isMounted) return;
        setSession(session);

        if (session?.user) {
          await fetchUserProfile(session.user);
        } else if (isMounted) {
          // Try to refresh the session if no active session is found
          const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();

          if (refreshError) {
            console.log('Session refresh failed:', refreshError.message);
            setUser(null);
          } else if (refreshData.session) {
            console.log('Session refreshed successfully');
            setSession(refreshData.session);
            await fetchUserProfile(refreshData.session.user);
          } else {
            setUser(null);
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        if (isMounted) {
          setUser(null);
        }
      }

      if (isMounted) {
        setIsLoading(false);
      }

      // Set up auth state change listener
      const { data: { subscription } } = supabase.auth.onAuthStateChange(
        async (_event, session) => {
          if (!isMounted) return;
          setSession(session);

          if (session?.user) {
            await fetchUserProfile(session.user);
          } else if (isMounted) {
            setUser(null);
          }

          if (isMounted) {
            setIsLoading(false);
          }
        }
      );

      // Clean up subscription on unmount
      return () => {
        subscription.unsubscribe();
      };
    };

    initializeAuth();

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, []);

  // Fetch user profile from the database - memoized with useCallback
  const fetchUserProfile = useCallback(async (supabaseUser: SupabaseUser) => {
    try {
      console.log('Fetching user profile for:', supabaseUser.id);

      // TEMPORARY WORKAROUND: Check if this is a known admin email
      // You should replace this with your actual admin email(s)
      const adminEmails = ['<EMAIL>']; // Admin email added
      const isAdminByEmail = adminEmails.includes(supabaseUser.email || '');

      if (isAdminByEmail) {
        console.log('Admin email detected, setting admin role directly');
        setUser({
          id: supabaseUser.id,
          email: supabaseUser.email || '',
          name: supabaseUser.email?.split('@')[0] || 'Admin',
          displayName: supabaseUser.email?.split('@')[0] || 'Admin',
          role: 'admin', // Force admin role for known admin emails
        });
        return;
      }

      // Try to get user profile from the database using service role to bypass RLS
      try {
        // First try with normal client (this might fail due to RLS issues)
        const { data: profile, error } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('id', supabaseUser.id)
          .single();

        if (error) {
          console.error('Error fetching profile with normal client:', error);
          throw error;
        }

        console.log('Profile data retrieved:', profile);

        // Create a unified user object
        const userData: User = {
          id: supabaseUser.id,
          email: supabaseUser.email || '',
          name: profile.display_name || supabaseUser.email?.split('@')[0] || 'User',
          displayName: profile.display_name || supabaseUser.email?.split('@')[0] || 'User',
          role: profile.role || 'user',
        };

        console.log('Setting user with role:', userData.role);
        setUser(userData);
      } catch (profileError) {
        // If normal client fails, use a direct SQL query as a fallback
        console.log('Trying alternative method to get user role...');

        // Check if user exists in user_profiles
        const { data: userExists, error: checkError } = await supabase
          .rpc('check_user_exists', { user_id: supabaseUser.id });

        if (checkError) {
          console.error('Error checking if user exists:', checkError);
          // If user doesn't exist, create a default profile
          const { error: createError } = await supabase
            .from('user_profiles')
            .insert({
              id: supabaseUser.id,
              display_name: supabaseUser.email?.split('@')[0] || 'User',
              email: supabaseUser.email,
              role: 'user' // Default role is always 'user'
            });

          if (createError) {
            console.error('Error creating user profile:', createError);
          }

          // Set user with default role
          setUser({
            id: supabaseUser.id,
            email: supabaseUser.email || '',
            name: supabaseUser.email?.split('@')[0] || 'User',
            displayName: supabaseUser.email?.split('@')[0] || 'User',
            role: 'user',
          });
        } else {
          // User exists, set with default role for now
          // You'll need to fix the RLS policies to make this work properly
          setUser({
            id: supabaseUser.id,
            email: supabaseUser.email || '',
            name: supabaseUser.email?.split('@')[0] || 'User',
            displayName: supabaseUser.email?.split('@')[0] || 'User',
            role: 'user', // Default to 'user' role until RLS is fixed
          });
        }
      }
    } catch (error) {
      console.error('Error in fetchUserProfile:', error);
      // If we can't get the profile, still set basic user info
      setUser({
        id: supabaseUser.id,
        email: supabaseUser.email || '',
        name: supabaseUser.email?.split('@')[0] || 'User',
        displayName: supabaseUser.email?.split('@')[0] || 'User',
        role: 'user', // Default to 'user' role if there's an error
      });
    }
  }, []);

  // Login with Supabase - memoized with useCallback
  const login = useCallback(async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);

      // Input validation
      if (!email || !email.trim() || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        toast({
          title: "Login failed",
          description: "Please enter a valid email address.",
          variant: "destructive"
        });
        return false;
      }

      if (!password || password.length < 6) {
        toast({
          title: "Login failed",
          description: "Password must be at least 6 characters.",
          variant: "destructive"
        });
        return false;
      }

      // Sign in with Supabase
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      toast({
        title: "Login successful",
        description: `Welcome back!`,
      });

      return true;
    } catch (error) {
      const authError = error as AuthError;
      console.error('Login error:', authError);

      toast({
        title: "Login failed",
        description: authError.message || "Invalid email or password. Please try again.",
        variant: "destructive"
      });

      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Register with Supabase - memoized with useCallback
  const register = useCallback(async (name: string, email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);

      // Input validation
      if (!name || !name.trim()) {
        toast({
          title: "Registration failed",
          description: "Name is required.",
          variant: "destructive"
        });
        return false;
      }

      if (!email || !email.trim() || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        toast({
          title: "Registration failed",
          description: "Please enter a valid email address.",
          variant: "destructive"
        });
        return false;
      }

      if (!password || password.length < 6) {
        toast({
          title: "Registration failed",
          description: "Password must be at least 6 characters.",
          variant: "destructive"
        });
        return false;
      }

      // Sign up with Supabase
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name: name,
          },
        },
      });

      if (error) throw error;

      // If user is created but needs email confirmation
      if (data.user && !data.session) {
        // Manually create user profile if the trigger failed
        try {
          // Check if profile exists
          const { data: profileData, error: profileError } = await supabase
            .from('user_profiles')
            .select('id')
            .eq('id', data.user.id)
            .maybeSingle();

          if (profileError || !profileData) {
            console.log('Creating user profile manually as fallback...');
            // Create profile manually
            await supabase
              .from('user_profiles')
              .insert({
                id: data.user.id,
                display_name: name,
                email: email,
                role: 'user',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              });
          }
        } catch (profileError) {
          console.error('Error creating user profile as fallback:', profileError);
          // Continue anyway - the user was created successfully
        }

        toast({
          title: "Registration successful",
          description: "Please check your email to confirm your account.",
        });
        return true;
      }

      // If user is created and automatically signed in
      if (data.user && data.session) {
        // Manually create user profile if the trigger failed
        try {
          // Check if profile exists
          const { data: profileData, error: profileError } = await supabase
            .from('user_profiles')
            .select('id')
            .eq('id', data.user.id)
            .maybeSingle();

          if (profileError || !profileData) {
            console.log('Creating user profile manually as fallback...');
            // Create profile manually
            await supabase
              .from('user_profiles')
              .insert({
                id: data.user.id,
                display_name: name,
                email: email,
                role: 'user',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              });
          }

          // Try to send welcome email
          if (data.user.email) {
            sendWelcomeEmail(data.user.id, data.user.email)
              .catch(emailError => console.error('Error sending welcome email:', emailError));
          }
        } catch (profileError) {
          console.error('Error creating user profile as fallback:', profileError);
          // Continue anyway - the user was created successfully
        }

        toast({
          title: "Registration successful",
          description: `Welcome, ${name}!`,
        });
        return true;
      }

      return false;
    } catch (error) {
      const authError = error as AuthError;
      console.error('Registration error:', authError);

      // Special handling for database errors
      if (authError.message?.includes('Database error') ||
          authError.message?.includes('saving new user')) {

        toast({
          title: "Registration failed",
          description: "There was an issue with the database. Please try again or contact support.",
          variant: "destructive"
        });
      } else {
        toast({
          title: "Registration failed",
          description: authError.message || "An unexpected error occurred. Please try again.",
          variant: "destructive"
        });
      }

      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Logout with Supabase - memoized with useCallback
  const logout = useCallback(async (): Promise<void> => {
    try {
      setIsLoading(true);

      // Get the user ID before signing out
      const currentUserId = user?.id;

      // Sign out with scope: 'local' to only remove from current browser
      const { error } = await supabase.auth.signOut({ scope: 'local' });

      if (error) throw error;

      // Clear session and user state
      setSession(null);
      setUser(null);

      // Clear user-specific cart from localStorage
      if (currentUserId) {
        localStorage.removeItem(`cart_${currentUserId}`);
      }

      // Clear any auth-related items from localStorage
      // This is a safety measure in case the Supabase signOut doesn't clean up properly
      localStorage.removeItem('badhees-auth-token');
      localStorage.removeItem('supabase.auth.token');

      // Note: We don't clear the guest cart, so it persists for non-logged-in users

      toast({
        title: "Logged out",
        description: "You have been successfully logged out.",
      });
    } catch (error) {
      console.error('Logout error:', error);

      toast({
        title: "Error",
        description: "There was an error logging out. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Update user profile - memoized with useCallback
  const updateUserProfile = useCallback(async (updatedData: Partial<User>): Promise<void> => {
    if (!user) return;

    try {
      // Update auth metadata if name is being updated
      if (updatedData.name) {
        const { error: authUpdateError } = await supabase.auth.updateUser({
          data: { name: updatedData.name }
        });

        if (authUpdateError) throw authUpdateError;
      }

      // Update profile in the database
      const { error: profileUpdateError } = await supabase
        .from('user_profiles')
        .update({
          display_name: updatedData.displayName || updatedData.name,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (profileUpdateError) throw profileUpdateError;

      // Update local user state
      setUser(prev => {
        if (!prev) return null;
        return {
          ...prev,
          name: updatedData.name || prev.name,
          displayName: updatedData.displayName || updatedData.name || prev.displayName,
        };
      });

      // Force a refresh of the user profile data
      const { data: refreshedProfile } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (refreshedProfile) {
        console.log('Profile refreshed after update:', refreshedProfile);
      }

      toast({
        title: "Profile updated",
        description: "Your profile information has been updated.",
      });
    } catch (error) {
      console.error('Error updating profile:', error);

      toast({
        title: "Update failed",
        description: "There was an error updating your profile. Please try again.",
        variant: "destructive"
      });
    }
  }, [user]);

  // Check if user is an admin - memoized with useCallback
  const isAdmin = useCallback((): boolean => {
    // Only return true if the user role is explicitly 'admin'
    return user?.role === 'admin';
  }, [user?.role]);

  // Login with Google OAuth - memoized with useCallback
  const loginWithGoogle = useCallback(async (): Promise<boolean> => {
    try {
      setIsLoading(true);

      // Sign in with Google using Supabase OAuth
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          }
        }
      });

      if (error) throw error;

      // The user will be redirected to Google for authentication
      // We'll return true to indicate the process has started
      return true;
    } catch (error) {
      console.error('Google login error:', error);
      toast({
        title: "Login failed",
        description: error instanceof Error ? error.message : "Failed to login with Google. Please try again.",
        variant: "destructive"
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Memoize the context value to prevent unnecessary re-renders
  const value = useMemo(() => ({
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    loginWithGoogle,
    register,
    logout,
    isAdmin,
    updateUserProfile,
  }), [
    user,
    isLoading,
    login,
    loginWithGoogle,
    register,
    logout,
    isAdmin,
    updateUserProfile
  ]);

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
