-- Create app_settings table to store global application settings
CREATE TABLE IF NOT EXISTS app_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  setting_key TEXT UNIQUE NOT NULL,
  setting_value JSONB NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  updated_by UUID REFERENCES auth.users(id)
);

-- Create index on setting_key for faster lookups
CREATE INDEX IF NOT EXISTS idx_app_settings_key ON app_settings(setting_key);

-- Insert default settings
INSERT INTO app_settings (setting_key, setting_value, description) VALUES
  ('payment_methods', '{"cod_enabled": true, "online_payment_enabled": true}', 'Payment method availability settings'),
  ('store_info', '{"name": "The Badhees", "email": "<EMAIL>", "phone": "+91-XXXXXXXXXX"}', 'Store basic information'),
  ('shipping_settings', '{"free_shipping_threshold": 499, "domestic_fee": 50, "estimated_days": "3-5"}', 'Shipping configuration')
ON CONFLICT (setting_key) DO NOTHING;

-- Enable RLS
ALTER TABLE app_settings ENABLE ROW LEVEL SECURITY;

-- Create policies for app_settings
-- Anyone can read settings (for frontend functionality)
CREATE POLICY "Anyone can view app settings" 
  ON app_settings FOR SELECT 
  USING (true);

-- Only admins can modify settings
CREATE POLICY "Only admins can modify app settings" 
  ON app_settings FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.id = auth.uid() 
      AND user_profiles.role = 'admin'
    )
  );

-- Create function to update settings with automatic timestamp
CREATE OR REPLACE FUNCTION update_app_setting(
  p_setting_key TEXT,
  p_setting_value JSONB,
  p_description TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if user is admin
  IF NOT EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE id = auth.uid() AND role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Only admins can update app settings';
  END IF;

  -- Update or insert setting
  INSERT INTO app_settings (setting_key, setting_value, description, updated_by)
  VALUES (p_setting_key, p_setting_value, p_description, auth.uid())
  ON CONFLICT (setting_key) 
  DO UPDATE SET 
    setting_value = EXCLUDED.setting_value,
    description = COALESCE(EXCLUDED.description, app_settings.description),
    updated_at = NOW(),
    updated_by = auth.uid();

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get setting value
CREATE OR REPLACE FUNCTION get_app_setting(p_setting_key TEXT)
RETURNS JSONB AS $$
DECLARE
  result JSONB;
BEGIN
  SELECT setting_value INTO result
  FROM app_settings
  WHERE setting_key = p_setting_key;
  
  RETURN COALESCE(result, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comments
COMMENT ON TABLE app_settings IS 'Global application settings and configuration';
COMMENT ON FUNCTION update_app_setting IS 'Update or insert application setting (admin only)';
COMMENT ON FUNCTION get_app_setting IS 'Get application setting value by key';
