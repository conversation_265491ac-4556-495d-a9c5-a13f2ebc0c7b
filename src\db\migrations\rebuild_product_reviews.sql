-- Drop existing tables, views, and functions if they exist
DROP VIEW IF EXISTS user_purchasable_reviews CASCADE;
DROP VIEW IF EXISTS product_ratings_summary CASCADE;
DROP FUNCTION IF EXISTS get_product_average_rating CASCADE;
DROP FUNCTION IF EXISTS has_user_purchased_product CASCADE;
DROP TABLE IF EXISTS product_reviews CASCADE;

-- Create product_reviews table
CREATE TABLE product_reviews (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id),
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  comment TEXT,
  title TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add index for faster lookups
CREATE INDEX idx_product_reviews_product_id ON product_reviews(product_id);
CREATE INDEX idx_product_reviews_user_id ON product_reviews(user_id);

-- Set up Row Level Security (RLS)
ALTER TABLE product_reviews ENABLE ROW LEVEL SECURITY;

-- Create a function to check if a user has purchased a product
CREATE OR REPLACE FUNCTION has_user_purchased_product(input_user_id UUID, input_product_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  has_purchased BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM orders o
    JOIN order_items oi ON o.id = oi.order_id
    WHERE o.user_id = input_user_id
    AND oi.product_id = input_product_id
    AND o.status IN ('delivered', 'shipped') -- Only count delivered or shipped orders
  ) INTO has_purchased;

  RETURN has_purchased;
END;
$$ LANGUAGE plpgsql;

-- Create a function to calculate average rating for a product
CREATE OR REPLACE FUNCTION get_product_average_rating(product_id UUID)
RETURNS TABLE (average_rating NUMERIC, review_count BIGINT) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COALESCE(ROUND(AVG(rating)::NUMERIC, 1), 0) AS average_rating,
    COUNT(*) AS review_count
  FROM product_reviews
  WHERE product_reviews.product_id = $1;
END;
$$ LANGUAGE plpgsql;

-- Create a view for product ratings summary
CREATE OR REPLACE VIEW product_ratings_summary AS
SELECT
  product_id,
  COALESCE(ROUND(AVG(rating)::NUMERIC, 1), 0) AS average_rating,
  COUNT(*) AS review_count
FROM product_reviews
GROUP BY product_id;

-- Create a view to show products a user has purchased but not yet reviewed
CREATE OR REPLACE VIEW user_purchasable_reviews AS
SELECT DISTINCT
  o.user_id,
  oi.product_id,
  p.name as product_name,
  o.id as order_id,
  o.created_at as purchase_date,
  CASE WHEN pr.id IS NULL THEN false ELSE true END as has_reviewed
FROM
  orders o
  JOIN order_items oi ON o.id = oi.order_id
  JOIN products p ON oi.product_id = p.id
  LEFT JOIN product_reviews pr ON pr.product_id = oi.product_id AND pr.user_id = o.user_id
WHERE
  o.status IN ('delivered', 'shipped');

-- Create policies for product_reviews
-- Anyone can view reviews
CREATE POLICY "Anyone can view product reviews"
  ON product_reviews FOR SELECT
  USING (true);

-- Users can insert reviews for purchased products
CREATE POLICY "Users can insert reviews for purchased products"
  ON product_reviews FOR INSERT
  WITH CHECK (
    auth.uid() = user_id AND
    has_user_purchased_product(auth.uid(), product_id)
  );

-- Users can update their own reviews
CREATE POLICY "Users can update their own reviews"
  ON product_reviews FOR UPDATE
  USING (auth.uid() = user_id);

-- Users can delete their own reviews
CREATE POLICY "Users can delete their own reviews"
  ON product_reviews FOR DELETE
  USING (auth.uid() = user_id);

-- Admins can manage all reviews
CREATE POLICY "Admins can manage all reviews"
  ON product_reviews FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Create a function to update product ratings in the products table
CREATE OR REPLACE FUNCTION update_product_rating()
RETURNS TRIGGER AS $$
DECLARE
  avg_rating NUMERIC;
  review_count BIGINT;
BEGIN
  -- Get the average rating and review count
  SELECT
    COALESCE(ROUND(AVG(rating)::NUMERIC, 1), 0),
    COUNT(*)
  INTO
    avg_rating,
    review_count
  FROM product_reviews
  WHERE product_id = COALESCE(NEW.product_id, OLD.product_id);

  -- Update the products table with the new rating information
  -- This assumes you have rating and review_count columns in your products table
  -- If not, you can modify this to use a separate ratings table
  BEGIN
    UPDATE products
    SET
      rating = avg_rating,
      review_count = review_count
    WHERE id = COALESCE(NEW.product_id, OLD.product_id);
  EXCEPTION WHEN OTHERS THEN
    -- If the update fails (e.g., columns don't exist), log the error but don't fail the transaction
    RAISE NOTICE 'Failed to update product rating: %', SQLERRM;
  END;

  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to update product ratings when reviews change
CREATE TRIGGER update_product_rating_insert
AFTER INSERT ON product_reviews
FOR EACH ROW
EXECUTE FUNCTION update_product_rating();

CREATE TRIGGER update_product_rating_update
AFTER UPDATE ON product_reviews
FOR EACH ROW
EXECUTE FUNCTION update_product_rating();

CREATE TRIGGER update_product_rating_delete
AFTER DELETE ON product_reviews
FOR EACH ROW
EXECUTE FUNCTION update_product_rating();

-- Add comments to explain the functions
COMMENT ON FUNCTION has_user_purchased_product IS 'Checks if a user has purchased a specific product';
COMMENT ON FUNCTION get_product_average_rating IS 'Calculates the average rating and review count for a product';
COMMENT ON FUNCTION update_product_rating IS 'Updates product rating in the products table when reviews change';
COMMENT ON VIEW product_ratings_summary IS 'Provides a summary of ratings for all products';
COMMENT ON VIEW user_purchasable_reviews IS 'Shows products a user has purchased and can review';
