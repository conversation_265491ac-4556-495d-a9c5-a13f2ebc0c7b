import React, { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';
import { getProducts } from '@/services/product/productService';
import { testSupabaseConnection } from '@/utils/supabaseHelpers';

const DebugSupabase = () => {
  const [connectionStatus, setConnectionStatus] = useState<string>('Testing...');
  const [products, setProducts] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [envVars, setEnvVars] = useState<any>({});

  useEffect(() => {
    const runTests = async () => {
      try {
        // Check environment variables
        const env = {
          VITE_SUPABASE_URL: import.meta.env.VITE_SUPABASE_URL,
          VITE_SUPABASE_ANON_KEY: import.meta.env.VITE_SUPABASE_ANON_KEY ? 'Set (hidden)' : 'Not set',
        };
        setEnvVars(env);

        // Test connection
        console.log('Testing Supabase connection...');
        const isConnected = await testSupabaseConnection();
        setConnectionStatus(isConnected ? 'Connected ✅' : 'Failed ❌');

        // Test direct query
        console.log('Testing direct products query...');
        const { data: directData, error: directError } = await supabase
          .from('products')
          .select('*')
          .limit(5);

        if (directError) {
          console.error('Direct query error:', directError);
          setError(`Direct query error: ${directError.message}`);
        } else {
          console.log('Direct query success:', directData);
        }

        // Test service function
        console.log('Testing getProducts service...');
        const serviceProducts = await getProducts();
        setProducts(serviceProducts);
        console.log('Service products:', serviceProducts);

      } catch (err) {
        console.error('Test error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      }
    };

    runTests();
  }, []);

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-6">Supabase Debug Page</h1>
      
      <div className="space-y-6">
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-xl font-semibold mb-2">Environment Variables</h2>
          <pre className="text-sm">{JSON.stringify(envVars, null, 2)}</pre>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-xl font-semibold mb-2">Connection Status</h2>
          <p className="text-lg">{connectionStatus}</p>
        </div>

        {error && (
          <div className="bg-red-100 p-4 rounded">
            <h2 className="text-xl font-semibold mb-2 text-red-800">Error</h2>
            <p className="text-red-700">{error}</p>
          </div>
        )}

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-xl font-semibold mb-2">Products ({products.length})</h2>
          {products.length > 0 ? (
            <div className="space-y-2">
              {products.slice(0, 3).map((product, index) => (
                <div key={index} className="bg-white p-2 rounded">
                  <p><strong>ID:</strong> {product.id}</p>
                  <p><strong>Name:</strong> {product.name}</p>
                  <p><strong>Price:</strong> ${product.price}</p>
                </div>
              ))}
            </div>
          ) : (
            <p>No products found</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default DebugSupabase;
