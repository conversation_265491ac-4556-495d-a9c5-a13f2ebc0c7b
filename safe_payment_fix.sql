-- SAFE Payment System Fix
-- Run each section separately to avoid errors

-- ========================================
-- SECTION 1: Check current data
-- ========================================
SELECT 'Current payment methods:' as info;
SELECT payment_method, COUNT(*) as count 
FROM orders 
GROUP BY payment_method 
ORDER BY count DESC;

-- ========================================
-- SECTION 2: Remove existing constraint first
-- ========================================
ALTER TABLE orders DROP CONSTRAINT IF EXISTS orders_payment_method_check;

-- ========================================
-- SECTION 3: Clean up payment method data
-- ========================================

-- Fix Cash on Delivery variations
UPDATE orders 
SET payment_method = 'cash_on_delivery' 
WHERE payment_method IN ('Cash on Delivery', 'COD', 'cash', 'cod', 'Cash', 'CASH_ON_DELIVERY')
   OR payment_method IS NULL 
   OR payment_method = '';

-- Fix Online Payment variations  
UPDATE orders 
SET payment_method = 'online_payment' 
WHERE payment_method IN ('Online Payment', 'online', 'Online', 'ONLINE_PAYMENT');

-- Fix Razorpay variations
UPDATE orders 
SET payment_method = 'razorpay' 
WHERE payment_method IN ('Razorpay', 'RAZORPAY', 'razor_pay');

-- Set any remaining unknown methods to cash_on_delivery
UPDATE orders 
SET payment_method = 'cash_on_delivery' 
WHERE payment_method NOT IN ('cash_on_delivery', 'online_payment', 'razorpay', 'upi', 'card', 'netbanking', 'wallet');

-- ========================================
-- SECTION 4: Verify data is clean
-- ========================================
SELECT 'After cleanup:' as info;
SELECT payment_method, COUNT(*) as count 
FROM orders 
GROUP BY payment_method 
ORDER BY count DESC;

-- ========================================
-- SECTION 5: Add constraint (should work now)
-- ========================================
ALTER TABLE orders ADD CONSTRAINT orders_payment_method_check 
CHECK (payment_method IN ('cash_on_delivery', 'online_payment', 'razorpay', 'upi', 'card', 'netbanking', 'wallet'));

-- ========================================
-- SECTION 6: Add missing columns
-- ========================================

-- Add payment_status column
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'orders' AND column_name = 'payment_status'
  ) THEN
    ALTER TABLE orders ADD COLUMN payment_status TEXT 
    CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')) 
    DEFAULT 'pending';
    
    -- Set default for existing orders
    UPDATE orders SET payment_status = 'pending' WHERE payment_status IS NULL;
  END IF;
END $$;

-- Add payment_reference column
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'orders' AND column_name = 'payment_reference'
  ) THEN
    ALTER TABLE orders ADD COLUMN payment_reference TEXT;
  END IF;
END $$;

-- ========================================
-- SECTION 7: Final verification
-- ========================================
SELECT 'Final result:' as info;
SELECT 
  payment_method, 
  payment_status,
  COUNT(*) as count 
FROM orders 
GROUP BY payment_method, payment_status 
ORDER BY payment_method, payment_status;
