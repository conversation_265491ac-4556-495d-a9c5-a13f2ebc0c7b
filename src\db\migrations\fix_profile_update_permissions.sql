-- Fix profile update permissions
-- This script specifically addresses the issue with updating user profiles

-- First, check if the user_profiles table exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_name = 'user_profiles'
  ) THEN
    RAISE EXCEPTION 'user_profiles table does not exist';
  END IF;
END $$;

-- Enable RLS on the user_profiles table (in case it's not enabled)
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Drop all existing policies to start fresh
DROP POLICY IF EXISTS "Users can view their own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON user_profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can insert profiles" ON user_profiles;
DROP POLICY IF EXISTS "Admins can delete profiles" ON user_profiles;
DROP POLICY IF EXISTS "Allow anonymous count of user_profiles" ON user_profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON user_profiles;

-- Create simplified policies with no complex conditions

-- 1. Allow users to view their own profile (simple condition)
CREATE POLICY "Users can view their own profile"
  ON user_profiles FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

-- 2. Allow users to update their own profile (simple condition)
CREATE POLICY "Users can update their own profile"
  ON user_profiles FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

-- 3. Allow users to insert their own profile (simple condition)
CREATE POLICY "Users can insert their own profile"
  ON user_profiles FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

-- 4. Allow admins to view all profiles
-- Using a simpler approach for admin check
CREATE POLICY "Admins can view all profiles"
  ON user_profiles FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 5. Allow admins to update all profiles
CREATE POLICY "Admins can update all profiles"
  ON user_profiles FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 6. Allow admins to insert profiles
CREATE POLICY "Admins can insert profiles"
  ON user_profiles FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 7. Allow admins to delete profiles
CREATE POLICY "Admins can delete profiles"
  ON user_profiles FOR DELETE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Create a function to check if a profile exists and create it if not
CREATE OR REPLACE FUNCTION ensure_user_profile(user_id uuid)
RETURNS boolean AS $$
BEGIN
  -- Check if profile exists
  IF EXISTS (SELECT 1 FROM user_profiles WHERE id = user_id) THEN
    RETURN true;
  ELSE
    -- Get user data
    DECLARE
      user_email text;
      user_name text;
    BEGIN
      SELECT email, raw_user_meta_data->>'name'
      INTO user_email, user_name
      FROM auth.users
      WHERE id = user_id;
      
      -- Insert new profile
      INSERT INTO user_profiles (
        id,
        display_name,
        email,
        role,
        created_at,
        updated_at
      ) VALUES (
        user_id,
        COALESCE(user_name, split_part(user_email, '@', 1)),
        user_email,
        'user',
        now(),
        now()
      );
      
      RETURN true;
    EXCEPTION
      WHEN OTHERS THEN
        RAISE WARNING 'Failed to create profile for user %: %', user_id, SQLERRM;
        RETURN false;
    END;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to update a user profile with better error handling
CREATE OR REPLACE FUNCTION update_user_profile(
  user_id uuid,
  p_display_name text DEFAULT NULL,
  p_phone text DEFAULT NULL,
  p_dob date DEFAULT NULL,
  p_street text DEFAULT NULL,
  p_city text DEFAULT NULL,
  p_state text DEFAULT NULL,
  p_postal_code text DEFAULT NULL,
  p_country text DEFAULT NULL,
  p_avatar_url text DEFAULT NULL
)
RETURNS boolean AS $$
BEGIN
  -- Ensure profile exists
  PERFORM ensure_user_profile(user_id);
  
  -- Update profile
  UPDATE user_profiles
  SET
    display_name = COALESCE(p_display_name, display_name),
    phone = COALESCE(p_phone, phone),
    dob = COALESCE(p_dob, dob),
    street = COALESCE(p_street, street),
    city = COALESCE(p_city, city),
    state = COALESCE(p_state, state),
    postal_code = COALESCE(p_postal_code, postal_code),
    country = COALESCE(p_country, country),
    avatar_url = COALESCE(p_avatar_url, avatar_url),
    updated_at = now()
  WHERE id = user_id;
  
  RETURN true;
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Failed to update profile for user %: %', user_id, SQLERRM;
    RETURN false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO postgres, anon, authenticated, service_role;

-- Create profiles for existing users who don't have one
INSERT INTO user_profiles (id, display_name, email, role, created_at, updated_at)
SELECT 
  id,
  COALESCE(raw_user_meta_data->>'name', split_part(email, '@', 1)),
  email,
  'user',
  now(),
  now()
FROM auth.users
WHERE NOT EXISTS (
  SELECT 1 FROM user_profiles WHERE user_profiles.id = auth.users.id
)
ON CONFLICT (id) DO NOTHING;
