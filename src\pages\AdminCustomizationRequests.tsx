import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import AdminSidebar from "@/components/admin/AdminSidebar";
import { useAuth } from '@/context/SupabaseAuthContext';
import { toast } from '@/hooks/use-toast';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { 
  getCustomizationRequests, 
  updateCustomizationRequestStatus,
  CustomizationRequest
} from '@/services/customizationRequestService';
import { formatDistanceToNow } from 'date-fns';
import { Search, Filter, Eye, Loader2 } from 'lucide-react';

const AdminCustomizationRequests = () => {
  const [requests, setRequests] = useState<CustomizationRequest[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<CustomizationRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedRequest, setSelectedRequest] = useState<CustomizationRequest | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  
  const navigate = useNavigate();
  const { isAuthenticated, isAdmin } = useAuth();

  // Check authentication and permissions
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login?redirect=/admin/customization-requests');
      toast({
        title: "Access Denied",
        description: "Please login to access the admin panel",
        variant: "destructive"
      });
    } else if (!isAdmin()) {
      navigate('/');
      toast({
        title: "Permission Denied",
        description: "You do not have permission to access the admin panel",
        variant: "destructive"
      });
    }
  }, [isAuthenticated, isAdmin, navigate]);

  // Fetch customization requests
  useEffect(() => {
    const fetchRequests = async () => {
      setIsLoading(true);
      try {
        const data = await getCustomizationRequests();
        setRequests(data);
        setFilteredRequests(data);
      } catch (error) {
        console.error('Error fetching customization requests:', error);
        toast({
          title: 'Error',
          description: 'Failed to load customization requests',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchRequests();
  }, []);

  // Filter requests when search query or status filter changes
  useEffect(() => {
    let result = [...requests];
    
    // Apply status filter
    if (statusFilter !== 'all') {
      result = result.filter(request => request.status === statusFilter);
    }
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        request =>
          request.name.toLowerCase().includes(query) ||
          request.email.toLowerCase().includes(query) ||
          request.description.toLowerCase().includes(query) ||
          request.product?.name.toLowerCase().includes(query)
      );
    }
    
    setFilteredRequests(result);
  }, [requests, searchQuery, statusFilter]);

  const handleViewRequest = (request: CustomizationRequest) => {
    setSelectedRequest(request);
    setIsViewDialogOpen(true);
  };

  const handleUpdateStatus = async (status: 'pending' | 'in_progress' | 'completed' | 'cancelled') => {
    if (!selectedRequest) return;
    
    setIsUpdating(true);
    try {
      const success = await updateCustomizationRequestStatus(selectedRequest.id!, status);
      
      if (success) {
        // Update the local state
        const updatedRequests = requests.map(req => 
          req.id === selectedRequest.id ? { ...req, status } : req
        );
        setRequests(updatedRequests);
        
        // Update the selected request
        setSelectedRequest({ ...selectedRequest, status });
      }
    } catch (error) {
      console.error('Error updating request status:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusBadge = (status?: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Pending</Badge>;
      case 'in_progress':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">In Progress</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Completed</Badge>;
      case 'cancelled':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Cancelled</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex-1 pt-28 pb-16">
        <div className="max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8 flex flex-col md:flex-row gap-8">
          <div className="md:w-64 flex-shrink-0">
            <AdminSidebar />
          </div>

          <div className="flex-1">
            <Breadcrumb className="mb-6">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/admin">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>Customization Requests</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>

            <Card>
              <CardHeader>
                <CardTitle>Customization Requests</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col sm:flex-row gap-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                    <Input
                      placeholder="Search requests..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <Filter size={18} className="text-gray-500" />
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Filter by status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="in_progress">In Progress</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="cancelled">Cancelled</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {isLoading ? (
                  <div className="flex justify-center items-center py-12">
                    <Loader2 className="h-8 w-8 animate-spin text-badhees-600" />
                  </div>
                ) : filteredRequests.length === 0 ? (
                  <div className="text-center py-12 text-gray-500">
                    {searchQuery || statusFilter !== 'all' ? (
                      <p>No matching customization requests found.</p>
                    ) : (
                      <p>No customization requests yet.</p>
                    )}
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Customer</TableHead>
                          <TableHead>Product</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredRequests.map((request) => (
                          <TableRow key={request.id}>
                            <TableCell>
                              <div>
                                <div className="font-medium">{request.name}</div>
                                <div className="text-sm text-gray-500">{request.email}</div>
                              </div>
                            </TableCell>
                            <TableCell>{request.product?.name || 'Unknown Product'}</TableCell>
                            <TableCell>
                              {request.created_at ? (
                                <span title={new Date(request.created_at).toLocaleString()}>
                                  {formatDistanceToNow(new Date(request.created_at), { addSuffix: true })}
                                </span>
                              ) : (
                                'Unknown'
                              )}
                            </TableCell>
                            <TableCell>{getStatusBadge(request.status)}</TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleViewRequest(request)}
                              >
                                <Eye className="h-4 w-4 mr-1" />
                                View
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* View Request Dialog */}
      {selectedRequest && (
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Customization Request Details</DialogTitle>
              <DialogDescription>
                Request from {selectedRequest.name} for {selectedRequest.product?.name || 'Unknown Product'}
              </DialogDescription>
            </DialogHeader>
            
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-medium">Status:</div>
                <div className="col-span-3">{getStatusBadge(selectedRequest.status)}</div>
              </div>
              
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-medium">Customer:</div>
                <div className="col-span-3">{selectedRequest.name}</div>
              </div>
              
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-medium">Email:</div>
                <div className="col-span-3">{selectedRequest.email}</div>
              </div>
              
              {selectedRequest.phone && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <div className="font-medium">Phone:</div>
                  <div className="col-span-3">{selectedRequest.phone}</div>
                </div>
              )}
              
              <div className="grid grid-cols-4 items-start gap-4">
                <div className="font-medium">Product:</div>
                <div className="col-span-3">{selectedRequest.product?.name || 'Unknown Product'}</div>
              </div>
              
              <div className="grid grid-cols-4 items-start gap-4">
                <div className="font-medium">Product ID:</div>
                <div className="col-span-3">{selectedRequest.product_id}</div>
              </div>
              
              <div className="grid grid-cols-4 items-start gap-4">
                <div className="font-medium">Description:</div>
                <div className="col-span-3 whitespace-pre-wrap">{selectedRequest.description}</div>
              </div>
              
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-medium">Date:</div>
                <div className="col-span-3">
                  {selectedRequest.created_at ? new Date(selectedRequest.created_at).toLocaleString() : 'Unknown'}
                </div>
              </div>
            </div>
            
            <DialogFooter className="flex-col sm:flex-row gap-2">
              <div className="flex-1 flex flex-wrap gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleUpdateStatus('pending')}
                  disabled={selectedRequest.status === 'pending' || isUpdating}
                  className={selectedRequest.status === 'pending' ? 'bg-yellow-50' : ''}
                >
                  {isUpdating ? <Loader2 className="h-4 w-4 mr-1 animate-spin" /> : null}
                  Mark as Pending
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleUpdateStatus('in_progress')}
                  disabled={selectedRequest.status === 'in_progress' || isUpdating}
                  className={selectedRequest.status === 'in_progress' ? 'bg-blue-50' : ''}
                >
                  {isUpdating ? <Loader2 className="h-4 w-4 mr-1 animate-spin" /> : null}
                  Mark as In Progress
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleUpdateStatus('completed')}
                  disabled={selectedRequest.status === 'completed' || isUpdating}
                  className={selectedRequest.status === 'completed' ? 'bg-green-50' : ''}
                >
                  {isUpdating ? <Loader2 className="h-4 w-4 mr-1 animate-spin" /> : null}
                  Mark as Completed
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleUpdateStatus('cancelled')}
                  disabled={selectedRequest.status === 'cancelled' || isUpdating}
                  className={selectedRequest.status === 'cancelled' ? 'bg-red-50' : ''}
                >
                  {isUpdating ? <Loader2 className="h-4 w-4 mr-1 animate-spin" /> : null}
                  Mark as Cancelled
                </Button>
              </div>
              <Button variant="secondary" onClick={() => setIsViewDialogOpen(false)}>
                Close
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      <Footer />
    </div>
  );
};

export default AdminCustomizationRequests;
