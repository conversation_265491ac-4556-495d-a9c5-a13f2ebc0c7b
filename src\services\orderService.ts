import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';
import { CartItem } from '@/context/SupabaseCartContext';
import { createRazorpayOrder } from '@/services/payment/razorpayService';
import {
  sendOrderConfirmationEmail,
  sendPaymentSuccessEmail,
  sendOrderStatusUpdateEmail
} from '@/services/emailService';

export interface OrderItem {
  id: string;
  order_id: string;
  product_id: string;
  quantity: number;
  price: number;
  product?: {
    name: string;
    image_url?: string;
    images?: Array<{image_url: string}>;
  };
}

export interface Order {
  id: string;
  user_id: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'canceled' | 'paid';
  payment_method: 'cash_on_delivery' | 'online_payment' | 'razorpay' | 'upi' | 'card' | 'netbanking' | 'wallet';
  payment_status?: 'pending' | 'paid' | 'failed' | 'refunded';
  razorpay_order_id?: string;
  razorpay_payment_id?: string;
  payment_reference?: string; // Transaction/Reference number
  payment_details?: {
    gateway?: string;
    transaction_id?: string;
    payment_method?: string;
    completed_at?: string;
    failure_reason?: string;
    failed_at?: string;
    [key: string]: any;
  };
  total_amount: number;
  shipping_address?: {
    name: string;
    street: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
    phone?: string;
  };
  billing_address?: {
    name: string;
    street: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
    phone?: string;
  };
  created_at: string;
  updated_at?: string;
  order_items?: OrderItem[];
  customer_name?: string; // For admin display
  user_profiles?: {
    display_name?: string;
    email?: string;
  };
  user?: Array<{
    display_name?: string;
    email?: string;
  }>;
}

// Payment Transaction interface
export interface PaymentTransaction {
  id: string;
  order_id: string;
  transaction_type: 'payment' | 'refund' | 'partial_refund';
  payment_method: string;
  payment_gateway?: string;
  gateway_transaction_id?: string;
  gateway_order_id?: string;
  amount: number;
  currency: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  gateway_response?: any;
  failure_reason?: string;
  user_id: string;
  created_at: string;
  updated_at: string;
}

// Type for database response
interface DbOrder extends Omit<Order, 'order_items'> {
  order_items?: DbOrderItem[];
}

interface DbOrderItem {
  id: string;
  order_id: string;
  product_id: string;
  quantity: number;
  price: number;
  product?: {
    name?: string;
    images?: Array<{image_url?: string}>;
  };
}

/**
 * Creates a new order in the database
 * @param userId The user ID
 * @param cartItems The cart items
 * @param totalAmount The total order amount
 * @param paymentMethod The payment method
 * @param shippingAddress The shipping address
 * @param billingAddress The billing address
 * @returns The created order or null if creation failed
 */
export const createOrder = async (
  userId: string,
  cartItems: CartItem[],
  totalAmount: number,
  paymentMethod: string,
  shippingAddress?: {
    name: string;
    street: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  },
  billingAddress?: {
    name: string;
    street: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  }
): Promise<Order | null> => {
  try {
    // Normalize payment method
    const normalizedPaymentMethod = paymentMethod === 'Cash on Delivery' ? 'cash_on_delivery' :
                                   paymentMethod === 'Online Payment' ? 'online_payment' :
                                   paymentMethod.toLowerCase().replace(/\s+/g, '_');

    // Create the order
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .insert([
        {
          user_id: userId,
          status: 'pending',
          payment_method: normalizedPaymentMethod,
          payment_status: normalizedPaymentMethod === 'cash_on_delivery' ? 'pending' : 'pending',
          total_amount: totalAmount,
          shipping_address: shippingAddress,
          billing_address: billingAddress || shippingAddress
        }
      ])
      .select()
      .single();

    if (orderError) {
      console.error('Error creating order:', orderError);
      toast({
        title: 'Error creating order',
        description: orderError.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      return null;
    }

    // Create order items using sale price if available
    const orderItems = cartItems.map(item => ({
      order_id: order.id,
      product_id: item.product.id,
      quantity: item.quantity,
      price: item.product.isSale && item.product.salePrice ? item.product.salePrice : item.product.price
    }));

    const { error: itemsError } = await supabase
      .from('order_items')
      .insert(orderItems);

    if (itemsError) {
      console.error('Error creating order items:', itemsError);
      toast({
        title: 'Error creating order items',
        description: itemsError.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      // Consider rolling back the order here
      return null;
    }

    // Create initial payment transaction record
    if (normalizedPaymentMethod === 'cash_on_delivery') {
      await createPaymentTransaction({
        order_id: order.id,
        transaction_type: 'payment',
        payment_method: 'cash_on_delivery',
        payment_gateway: null,
        amount: totalAmount,
        currency: 'INR',
        status: 'pending',
        user_id: userId
      });
    }

    toast({
      title: 'Order placed successfully',
      description: `Your order #${order.id.substring(0, 8)} has been placed.`,
    });

    // Get user email
    const { data: userData, error: userError } = await supabase
      .from('user_profiles')
      .select('email')
      .eq('id', userId)
      .single();

    // Send order confirmation email
    if (!userError && userData?.email) {
      sendOrderConfirmationEmail(order.id, userData.email)
        .catch(error => console.error('Error sending order confirmation email:', error));
    }

    return order;
  } catch (error) {
    console.error('Error in createOrder:', error);
    toast({
      title: 'Error placing order',
      description: 'An unexpected error occurred while placing your order.',
      variant: 'destructive',
    });
    return null;
  }
};

/**
 * Gets all orders for a user
 * @param userId The user ID
 * @returns Array of orders
 */
export const getUserOrders = async (userId: string): Promise<Order[]> => {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        order_items:order_items(
          *,
          product:products(name, images:product_images(image_url))
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      // Check if the error is related to the foreign key relationship
      if (error.message?.includes('foreign key') ||
          error.message?.includes('relationship') ||
          error.message?.includes('auth.users')) {
        console.error('Foreign key relationship error in getUserOrders:', error);
        console.log('This can be fixed by running the fix_orders_user_relationship.sql migration');

        // Return empty array to avoid breaking the UI
        return [];
      }

      console.error('Error fetching user orders:', error);
      return [];
    }

    // Process the data to format it correctly
    return data.map((order: DbOrder) => {
      // Format order items to include product details
      const orderItems = (order.order_items || []).map((item: DbOrderItem) => ({
        ...item,
        product: {
          name: item.product?.name || 'Unknown Product',
          image_url: item.product?.images?.[0]?.image_url || ''
        }
      }));

      return {
        ...order,
        order_items: orderItems
      };
    });
  } catch (error) {
    console.error('Error in getUserOrders:', error);
    return [];
  }
};

/**
 * Gets all orders (admin function)
 * @param status Optional status filter
 * @returns Array of orders
 */
export const getAllOrders = async (status?: string): Promise<Order[]> => {
  try {
    console.log('[OrderService] Fetching all orders with status filter:', status);

    // Start with the simplest approach - basic orders query first
    console.log('[OrderService] Attempting basic orders query...');

    let basicQuery = supabase
      .from('orders')
      .select('*')
      .order('created_at', { ascending: false });

    // Apply status filter if provided
    if (status && status !== 'all') {
      basicQuery = basicQuery.eq('status', status);
    }

    const { data: basicData, error: basicError } = await basicQuery;

    if (basicError) {
      console.error('[OrderService] Basic orders query failed:', basicError);
      return [];
    }

    if (!basicData || basicData.length === 0) {
      console.log('[OrderService] No orders found');
      return [];
    }

    console.log(`[OrderService] Found ${basicData.length} orders, now fetching order items...`);

    // Now try to get order items for each order
    const ordersWithItems = await Promise.all(
      basicData.map(async (order: DbOrder) => {
        try {
          // Get order items
          const { data: orderItems, error: itemsError } = await supabase
            .from('order_items')
            .select(`
              *,
              product:products(name, images:product_images(image_url))
            `)
            .eq('order_id', order.id);

          if (itemsError) {
            console.warn(`[OrderService] Error fetching items for order ${order.id}:`, itemsError);
          }

          // Format order items
          const formattedItems = (orderItems || []).map((item: DbOrderItem) => ({
            ...item,
            product: {
              name: item.product?.name || 'Unknown Product',
              image_url: item.product?.images?.[0]?.image_url || ''
            }
          }));

          return {
            ...order,
            order_items: formattedItems,
            customer_name: 'Customer ID: ' + order.user_id.substring(0, 8)
          };
        } catch (itemError) {
          console.warn(`[OrderService] Exception fetching items for order ${order.id}:`, itemError);
          return {
            ...order,
            order_items: [],
            customer_name: 'Customer ID: ' + order.user_id.substring(0, 8)
          };
        }
      })
    );

    console.log(`[OrderService] Successfully processed ${ordersWithItems.length} orders`);
    return ordersWithItems;

  } catch (error) {
    console.error('[OrderService] Unexpected error in getAllOrders:', error);
    return [];
  }
};

/**
 * Gets a single order by ID
 * @param orderId The order ID
 * @returns The order or null if not found
 */
export const getOrderById = async (orderId: string): Promise<Order | null> => {
  try {
    console.log('[OrderService] Fetching order by ID:', orderId);

    // Start with basic order query
    const { data: orderData, error: orderError } = await supabase
      .from('orders')
      .select('*')
      .eq('id', orderId)
      .single();

    if (orderError) {
      console.error('[OrderService] Error fetching order:', orderError);
      return null;
    }

    if (!orderData) {
      console.log('[OrderService] Order not found');
      return null;
    }

    console.log('[OrderService] Order found, fetching order items...');

    // Get order items separately
    const { data: orderItems, error: itemsError } = await supabase
      .from('order_items')
      .select(`
        *,
        product:products(name, images:product_images(image_url))
      `)
      .eq('order_id', orderId);

    if (itemsError) {
      console.warn('[OrderService] Error fetching order items:', itemsError);
    }

    // Format order items
    const formattedItems = (orderItems || []).map((item: DbOrderItem) => ({
      ...item,
      product: {
        name: item.product?.name || 'Unknown Product',
        image_url: item.product?.images?.[0]?.image_url || ''
      }
    }));

    console.log('[OrderService] Successfully fetched order with items');

    return {
      ...orderData,
      order_items: formattedItems,
      customer_name: 'Customer ID: ' + orderData.user_id.substring(0, 8)
    };
  } catch (error) {
    console.error('[OrderService] Unexpected error in getOrderById:', error);
    return null;
  }
};

/**
 * Updates an order's status
 * @param orderId The order ID
 * @param status The new status
 * @returns True if successful, false otherwise
 */
export const updateOrderStatus = async (
  orderId: string,
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'canceled' | 'paid'
): Promise<boolean> => {
  try {
    // Add updated_at timestamp to track when the status was changed
    const updateData: any = {
      status,
      updated_at: new Date().toISOString()
    };

    // If status is 'paid', also update payment_status
    if (status === 'paid') {
      updateData.payment_status = 'paid';
    }

    const { error } = await supabase
      .from('orders')
      .update(updateData)
      .eq('id', orderId);

    if (error) {
      // Check for specific error types
      if (error.code === 'PGRST202') {
        console.error('Function not found error in updateOrderStatus:', error);
        toast({
          title: 'Database configuration error',
          description: 'There appears to be an issue with the database configuration. Please contact support.',
          variant: 'destructive',
        });
      } else if (error.code === '42501') {
        console.error('Permission denied error in updateOrderStatus:', error);
        toast({
          title: 'Permission denied',
          description: 'You do not have permission to update this order. Please check your account privileges.',
          variant: 'destructive',
        });
      } else {
        console.error('Error updating order status:', error);
        toast({
          title: 'Error updating order',
          description: error.message || 'An unexpected error occurred',
          variant: 'destructive',
        });
      }
      return false;
    }

    toast({
      title: 'Order updated',
      description: `Order status has been updated to ${status}.`,
    });

    // Send email notification for certain status changes
    if (['shipped', 'delivered', 'canceled'].includes(status)) {
      try {
        // Get order details to get user ID
        const { data: orderData, error: orderFetchError } = await supabase
          .from('orders')
          .select('user_id')
          .eq('id', orderId)
          .single();

        if (!orderFetchError) {
          // Get user email
          const { data: userData, error: userError } = await supabase
            .from('user_profiles')
            .select('email')
            .eq('id', orderData.user_id)
            .single();

          // Send order status update email
          if (!userError && userData?.email) {
            sendOrderStatusUpdateEmail(orderId, userData.email, status)
              .catch(error => console.error('Error sending order status update email:', error));
          }
        }
      } catch (emailError) {
        console.error('Error sending order status update email:', emailError);
        // Don't return false here, as the order status was successfully updated
      }
    }

    return true;
  } catch (error) {
    console.error('Error in updateOrderStatus:', error);
    toast({
      title: 'Error updating order',
      description: 'An unexpected error occurred while updating the order status.',
      variant: 'destructive',
    });
    return false;
  }
};

/**
 * Creates a Razorpay order for an existing order
 * @param order The order to create a Razorpay order for
 * @param userEmail User's email for Razorpay notes
 * @returns The Razorpay order ID or null if creation failed
 */
export const createRazorpayOrderForOrder = async (
  order: Order,
  userEmail: string
): Promise<string | null> => {
  try {
    // Create Razorpay order
    const razorpayOrder = await createRazorpayOrder(
      order.total_amount,
      'INR',
      order.id,
      {
        user_id: order.user_id,
        email: userEmail,
        shipping_address: order.shipping_address
      }
    );

    if (!razorpayOrder || !razorpayOrder.id) {
      console.error('Failed to create Razorpay order');
      return null;
    }

    // Update order with Razorpay order ID
    const { error } = await supabase
      .from('orders')
      .update({
        razorpay_order_id: razorpayOrder.id,
        payment_status: 'pending',
        updated_at: new Date().toISOString()
      })
      .eq('id', order.id);

    if (error) {
      console.error('Error updating order with Razorpay order ID:', error);
      return null;
    }

    // Create payment transaction record for Razorpay order
    await createPaymentTransaction({
      order_id: order.id,
      transaction_type: 'payment',
      payment_method: 'online_payment',
      payment_gateway: 'razorpay',
      gateway_order_id: razorpayOrder.id,
      amount: order.total_amount,
      currency: 'INR',
      status: 'pending',
      user_id: order.user_id
    });

    return razorpayOrder.id;
  } catch (error) {
    console.error('Error creating Razorpay order:', error);
    return null;
  }
};

/**
 * Creates a payment transaction record
 * @param transactionData Payment transaction data
 * @returns The created transaction or null if failed
 */
export const createPaymentTransaction = async (
  transactionData: Omit<PaymentTransaction, 'id' | 'created_at' | 'updated_at'>
): Promise<PaymentTransaction | null> => {
  try {
    const { data, error } = await supabase
      .from('payment_transactions')
      .insert([transactionData])
      .select()
      .single();

    if (error) {
      console.error('Error creating payment transaction:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in createPaymentTransaction:', error);
    return null;
  }
};

/**
 * Updates a payment transaction status
 * @param transactionId The transaction ID
 * @param status The new status
 * @param gatewayResponse Optional gateway response data
 * @param failureReason Optional failure reason
 * @returns True if successful, false otherwise
 */
export const updatePaymentTransactionStatus = async (
  transactionId: string,
  status: PaymentTransaction['status'],
  gatewayResponse?: any,
  failureReason?: string
): Promise<boolean> => {
  try {
    const updateData: any = {
      status,
      updated_at: new Date().toISOString()
    };

    if (gatewayResponse) {
      updateData.gateway_response = gatewayResponse;
    }

    if (failureReason) {
      updateData.failure_reason = failureReason;
    }

    const { error } = await supabase
      .from('payment_transactions')
      .update(updateData)
      .eq('id', transactionId);

    if (error) {
      console.error('Error updating payment transaction:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in updatePaymentTransactionStatus:', error);
    return false;
  }
};

/**
 * Gets payment transactions for an order
 * @param orderId The order ID
 * @returns Array of payment transactions
 */
export const getOrderPaymentTransactions = async (orderId: string): Promise<PaymentTransaction[]> => {
  try {
    const { data, error } = await supabase
      .from('payment_transactions')
      .select('*')
      .eq('order_id', orderId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching payment transactions:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getOrderPaymentTransactions:', error);
    return [];
  }
};

/**
 * Updates an order with Razorpay payment details
 * @param orderId The order ID
 * @param razorpayPaymentId The Razorpay payment ID
 * @returns True if successful, false otherwise
 */
export const updateOrderWithPayment = async (
  orderId: string,
  razorpayPaymentId: string
): Promise<boolean> => {
  try {
    // Get order details first to get user ID
    const { data: orderData, error: orderFetchError } = await supabase
      .from('orders')
      .select('user_id')
      .eq('id', orderId)
      .single();

    if (orderFetchError) {
      console.error('Error fetching order for payment update:', orderFetchError);
      return false;
    }

    // Update the order
    const { error } = await supabase
      .from('orders')
      .update({
        razorpay_payment_id: razorpayPaymentId,
        payment_status: 'paid',
        status: 'paid',
        payment_reference: razorpayPaymentId,
        updated_at: new Date().toISOString()
      })
      .eq('id', orderId);

    if (error) {
      console.error('Error updating order with payment details:', error);
      return false;
    }

    // Update payment transaction status
    const { data: transactions } = await supabase
      .from('payment_transactions')
      .select('id')
      .eq('order_id', orderId)
      .eq('payment_gateway', 'razorpay')
      .eq('status', 'pending')
      .order('created_at', { ascending: false })
      .limit(1);

    if (transactions && transactions.length > 0) {
      await updatePaymentTransactionStatus(
        transactions[0].id,
        'completed',
        { razorpay_payment_id: razorpayPaymentId }
      );
    }

    // Get user email
    const { data: userData, error: userError } = await supabase
      .from('user_profiles')
      .select('email')
      .eq('id', orderData.user_id)
      .single();

    // Send payment success email
    if (!userError && userData?.email) {
      sendPaymentSuccessEmail(orderId, userData.email, razorpayPaymentId)
        .catch(error => console.error('Error sending payment success email:', error));
    }

    return true;
  } catch (error) {
    console.error('Error in updateOrderWithPayment:', error);
    return false;
  }
};
