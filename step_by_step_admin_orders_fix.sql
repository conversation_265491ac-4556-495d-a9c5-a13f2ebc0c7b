-- STEP-BY-STEP FIX FOR ADMIN ORDERS ISSUES
-- Execute each section one by one and check for errors

-- ============================================================================
-- STEP 1: BASIC DIAGNOSTICS - Check what exists
-- ============================================================================

-- Check if orders table exists and its structure
SELECT 'STEP 1A: Orders table structure' as step;
SELECT 
  column_name,
  data_type,
  is_nullable
FROM information_schema.columns 
WHERE table_name = 'orders' 
ORDER BY ordinal_position;

-- Check if user_profiles table exists
SELECT 'STEP 1B: User profiles table check' as step;
SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles') 
    THEN 'user_profiles table EXISTS'
    ELSE 'user_profiles table DOES NOT EXIST'
  END as status;

-- Check if we can query orders table at all
SELECT 'STEP 1C: Basic orders query test' as step;
SELECT COUNT(*) as order_count FROM orders;

-- ============================================================================
-- STEP 2: CREATE USER_PROFILES TABLE (SIMPLE VERSION)
-- ============================================================================

SELECT 'STEP 2: Creating user_profiles table' as step;

-- Drop the table if it exists (to start fresh)
DROP TABLE IF EXISTS user_profiles CASCADE;

-- Create a simple user_profiles table
CREATE TABLE user_profiles (
  id UUID PRIMARY KEY,
  display_name TEXT,
  email TEXT,
  role TEXT DEFAULT 'user',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add some basic indexes
CREATE INDEX idx_user_profiles_id ON user_profiles(id);
CREATE INDEX idx_user_profiles_email ON user_profiles(email);

SELECT 'User profiles table created successfully' as result;

-- ============================================================================
-- STEP 3: POPULATE USER_PROFILES WITH SAMPLE DATA
-- ============================================================================

SELECT 'STEP 3: Populating user_profiles with sample data' as step;

-- Insert sample user profiles for existing orders
INSERT INTO user_profiles (id, display_name, email, role)
SELECT DISTINCT 
  o.user_id,
  'Customer ' || SUBSTRING(o.user_id::text, 1, 8),
  'customer' || SUBSTRING(o.user_id::text, 1, 8) || '@example.com',
  'user'
FROM orders o
WHERE o.user_id IS NOT NULL
ON CONFLICT (id) DO NOTHING;

-- Check how many profiles were created
SELECT COUNT(*) as profiles_created FROM user_profiles;

-- ============================================================================
-- STEP 4: SET UP BASIC PERMISSIONS (NO RLS FOR NOW)
-- ============================================================================

SELECT 'STEP 4: Setting up basic permissions' as step;

-- Disable RLS for now to avoid permission issues
ALTER TABLE user_profiles DISABLE ROW LEVEL SECURITY;

-- Grant basic permissions
GRANT SELECT ON user_profiles TO anon, authenticated;
GRANT INSERT, UPDATE, DELETE ON user_profiles TO authenticated;

SELECT 'Permissions granted successfully' as result;

-- ============================================================================
-- STEP 5: TEST BASIC QUERIES
-- ============================================================================

SELECT 'STEP 5: Testing basic queries' as step;

-- Test orders query
SELECT 'Orders count:' as test, COUNT(*) as count FROM orders;

-- Test user_profiles query
SELECT 'User profiles count:' as test, COUNT(*) as count FROM user_profiles;

-- Test simple join
SELECT 'Join test:' as test, COUNT(*) as count 
FROM orders o 
LEFT JOIN user_profiles up ON o.user_id = up.id;

-- ============================================================================
-- STEP 6: CREATE SIMPLE VIEW (NO COMPLEX JOINS)
-- ============================================================================

SELECT 'STEP 6: Creating simple orders view' as step;

-- Drop existing view if any
DROP VIEW IF EXISTS orders_with_user_info CASCADE;

-- Create a simple view
CREATE VIEW orders_with_user_info AS
SELECT 
  o.*,
  COALESCE(up.display_name, 'Customer ' || SUBSTRING(o.user_id::text, 1, 8)) as customer_name,
  up.email as customer_email
FROM orders o
LEFT JOIN user_profiles up ON o.user_id = up.id;

-- Grant permissions on the view
GRANT SELECT ON orders_with_user_info TO anon, authenticated;

SELECT 'View created successfully' as result;

-- ============================================================================
-- STEP 7: TEST THE VIEW
-- ============================================================================

SELECT 'STEP 7: Testing the view' as step;

-- Test the view
SELECT COUNT(*) as view_count FROM orders_with_user_info;

-- Test with sample data
SELECT 
  id,
  user_id,
  customer_name,
  status,
  total_amount
FROM orders_with_user_info 
LIMIT 3;

-- ============================================================================
-- STEP 8: FINAL VERIFICATION
-- ============================================================================

SELECT 'STEP 8: Final verification' as step;

-- Check that everything is working
SELECT 
  'Orders table' as table_name,
  COUNT(*) as record_count
FROM orders
UNION ALL
SELECT 
  'User profiles table' as table_name,
  COUNT(*) as record_count
FROM user_profiles
UNION ALL
SELECT 
  'Orders with user info view' as table_name,
  COUNT(*) as record_count
FROM orders_with_user_info;

-- Success message
SELECT 'SUCCESS: All components created and tested!' as final_status;
