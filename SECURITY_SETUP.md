# 🔒 Security Setup Guide

## ⚠️ IMPORTANT SECURITY NOTICE

This project has been configured with proper security practices. Follow this guide to set up your environment safely.

## 🔐 Environment Variables Setup

### 1. Local Development Setup

1. **Copy the environment template:**
   ```bash
   cp .env .env.local
   ```

2. **Add your actual credentials to `.env.local`:**
   ```env
   # Supabase Configuration
   VITE_SUPABASE_URL=https://your-actual-project-id.supabase.co
   VITE_SUPABASE_ANON_KEY=your_actual_supabase_anon_key

   # Razorpay Configuration (for payments)
   VITE_RAZORPAY_KEY_ID=rzp_test_your_actual_key_id
   RAZORPAY_KEY_ID=rzp_test_your_actual_key_id
   RAZORPAY_SECRET=your_actual_razorpay_secret
   ```

3. **Never commit `.env.local`** - it's already gitignored

### 2. Production Deployment

For production deployment (Vercel, Netlify, etc.):

1. **Set environment variables in your hosting platform's dashboard**
2. **Use production keys:**
   ```env
   VITE_SUPABASE_URL=your_production_supabase_url
   VITE_SUPABASE_ANON_KEY=your_production_anon_key
   VITE_RAZORPAY_KEY_ID=rzp_live_your_live_key_id
   RAZORPAY_KEY_ID=rzp_live_your_live_key_id
   RAZORPAY_SECRET=your_live_razorpay_secret
   ```

## 🛡️ Security Features Implemented

### Frontend Security
- ✅ Environment variables properly configured
- ✅ Only public keys exposed to frontend
- ✅ Sensitive credentials kept server-side only
- ✅ Proper gitignore configuration

### Backend Security
- ✅ Private API keys kept in server environment
- ✅ Webhook secrets for payment verification
- ✅ Supabase RLS (Row Level Security) enabled
- ✅ Authentication and authorization implemented

### File Security
- ✅ `.env.local` - gitignored (contains actual credentials)
- ✅ `.env` - template file (safe to commit)
- ✅ `.env.example` - documentation template
- ✅ All sensitive files properly gitignored

## 🚨 Security Checklist

### Before Committing Code:
- [ ] No actual credentials in committed files
- [ ] `.env.local` contains actual credentials (gitignored)
- [ ] `.env` contains only template values
- [ ] No hardcoded API keys in source code
- [ ] All sensitive files in `.gitignore`

### Before Deployment:
- [ ] Environment variables set in hosting platform
- [ ] Production keys configured (not test keys)
- [ ] Debug mode disabled in production
- [ ] HTTPS enabled for production domain
- [ ] Webhook URLs updated for production

## 🔍 How to Verify Security

### Check for Exposed Credentials:
```bash
# Search for potential credential leaks
grep -r "eyJ" src/ --exclude-dir=node_modules
grep -r "rzp_" src/ --exclude-dir=node_modules
grep -r "sk_" src/ --exclude-dir=node_modules
```

### Verify Environment Setup:
```bash
# Check that .env.local exists and contains actual values
cat .env.local | grep -v "your_"

# Check that .env contains only templates
cat .env | grep "your_"
```

## 📞 Emergency Response

If credentials are accidentally committed:

1. **Immediately rotate all exposed keys**
2. **Remove sensitive commits from git history**
3. **Update all environment configurations**
4. **Notify team members of the security incident**

## 🎯 Best Practices

1. **Never commit actual credentials**
2. **Use different keys for development and production**
3. **Regularly rotate API keys**
4. **Monitor for unauthorized access**
5. **Keep dependencies updated**
6. **Use HTTPS in production**
7. **Enable proper CORS settings**

## 📚 Additional Resources

- [Supabase Security Guide](https://supabase.com/docs/guides/auth)
- [Razorpay Security Best Practices](https://razorpay.com/docs/security/)
- [Vercel Environment Variables](https://vercel.com/docs/concepts/projects/environment-variables)

---

**Remember: Security is everyone's responsibility. When in doubt, ask for a security review!**
