
import { useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import ResponsiveLayout from "@/components/layout/ResponsiveLayout";
import PageContainer from "@/components/layout/PageContainer";
import { Minus, Plus, X, ShoppingBag, CreditCard, ArrowRight } from "lucide-react";
import { useCart } from "@/context/SupabaseCartContext";
import { useAuth } from "@/context/SupabaseAuthContext";
import { Button } from "@/components/ui/button";
// import { useIsMobile } from "@/hooks/use-responsive";

const Cart = () => {
  const { cartItems, updateQuantity, removeFromCart, subtotal } = useCart();
  const { isAuthenticated, isAdmin } = useAuth();
  const navigate = useNavigate();



  useEffect(() => {
    window.scrollTo(0, 0);

    // If user is an admin, redirect them away from cart as admins shouldn't have customer carts
    if (isAdmin()) {
      navigate('/admin');
      return;
    }
  }, [navigate, isAdmin]);

  // No shipping or tax charges
  const total = subtotal;

  const handleCheckout = () => {
    if (isAuthenticated) {
      navigate('/checkout');
    } else {
      navigate('/login?redirect=checkout');
    }
  };

  if (cartItems.length === 0) {
    return (
      <ResponsiveLayout>
        <PageContainer>
          <div className="flex flex-col items-center justify-center py-16 md:py-32">
            <div className="bg-badhees-50 p-4 md:p-6 rounded-full mb-4 md:mb-6">
              <ShoppingBag className="h-8 w-8 md:h-12 md:w-12 text-badhees-600" />
            </div>
            <h1 className="text-xl md:text-2xl font-bold text-badhees-800 mb-3">Your Cart is Empty</h1>
            <p className="text-center text-badhees-600 mb-6 md:mb-8 max-w-md px-4">
              Looks like you haven't added anything to your cart yet. Explore our collections to find something you love.
            </p>
            <Button asChild>
              <Link to="/products">Start Shopping</Link>
            </Button>
          </div>
        </PageContainer>
      </ResponsiveLayout>
    );
  }

  return (
    <ResponsiveLayout>
      <PageContainer>
        <h1 className="text-xl md:text-2xl lg:text-3xl font-bold text-badhees-800 mb-4 md:mb-6">
          Shopping Cart
        </h1>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl overflow-hidden shadow-sm border border-badhees-100">
              <div className="p-4 md:p-6">
                <div className="hidden md:grid md:grid-cols-12 text-sm font-medium text-badhees-500 pb-4 border-b border-badhees-100">
                  <div className="md:col-span-6">Product</div>
                  <div className="md:col-span-2 text-center">Price</div>
                  <div className="md:col-span-2 text-center">Quantity</div>
                  <div className="md:col-span-2 text-right">Total</div>
                </div>

                <div className="divide-y divide-badhees-100">
                  {cartItems.map((item) => (
                    <div key={item.product.id} className="py-3 md:py-6 grid grid-cols-1 md:grid-cols-12 gap-3 md:gap-4 items-center">
                      {/* Product Image and Info */}
                      <div className="md:col-span-6 flex items-center space-x-3 md:space-x-4">
                        <div className="w-16 h-16 md:w-20 md:h-20 rounded-lg overflow-hidden bg-badhees-50 flex-shrink-0">
                          <img
                            src={item.product.image}
                            alt={item.product.name}
                            className="w-full h-full object-cover"
                            loading="lazy"
                          />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium text-sm md:text-base text-badhees-800">{item.product.name}</h3>
                          <div className="md:hidden text-xs text-badhees-500 mt-1">
                            {item.product.isSale && item.product.salePrice ? (
                              <div className="flex items-center space-x-2">
                                <span className="text-badhees-accent font-medium">₹{item.product.salePrice.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
                                <span className="line-through text-badhees-400">₹{item.product.price.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
                              </div>
                            ) : (
                              <span>₹{item.product.price.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
                            )}
                          </div>
                          <button
                            type="button"
                            onClick={() => removeFromCart(item.product.id)}
                            className="text-xs text-badhees-500 hover:text-badhees-700 flex items-center mt-1 touch-target py-1"
                          >
                            <X className="h-3 w-3 mr-1" />
                            Remove
                          </button>
                        </div>
                      </div>

                      {/* Price - Desktop only */}
                      <div className="hidden md:block md:col-span-2 text-center text-badhees-800">
                        {item.product.isSale && item.product.salePrice ? (
                          <div className="flex flex-col items-center">
                            <span className="text-badhees-accent font-medium">₹{item.product.salePrice.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
                            <span className="text-xs line-through text-badhees-400">₹{item.product.price.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
                          </div>
                        ) : (
                          <span>₹{item.product.price.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
                        )}
                      </div>

                      {/* Mobile layout for quantity and total */}
                      <div className="md:hidden flex justify-between items-center w-full mt-1">
                        {/* Quantity */}
                        <div className="flex items-center h-8 border border-badhees-200 rounded-md">
                          <button
                            type="button"
                            onClick={() => updateQuantity(item.product.id, Math.max(1, item.quantity - 1))}
                            className="w-8 h-full flex items-center justify-center text-badhees-600 hover:text-badhees-800 transition-colors touch-target"
                            aria-label="Decrease quantity"
                            disabled={item.quantity <= 1}
                          >
                            <Minus className="h-3 w-3" />
                          </button>
                          <input
                            type="number"
                            value={item.quantity}
                            onChange={(e) => updateQuantity(item.product.id, parseInt(e.target.value) || 1)}
                            className="w-8 h-full text-center border-0 focus:outline-none focus:ring-0 text-badhees-800 text-sm"
                            min="1"
                            aria-label="Quantity"
                            title="Quantity"
                          />
                          <button
                            type="button"
                            onClick={() => updateQuantity(item.product.id, item.quantity + 1)}
                            className="w-8 h-full flex items-center justify-center text-badhees-600 hover:text-badhees-800 transition-colors touch-target"
                            aria-label="Increase quantity"
                          >
                            <Plus className="h-3 w-3" />
                          </button>
                        </div>

                        {/* Total */}
                        <div className="text-right">
                          <div className="font-medium text-sm text-badhees-800">
                            ₹{((item.product.isSale && item.product.salePrice ? item.product.salePrice : item.product.price) * item.quantity).toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}
                          </div>
                        </div>
                      </div>

                      {/* Desktop layout for quantity and total */}
                      <div className="hidden md:flex md:col-span-2 justify-center">
                        <div className="flex items-center h-10 border border-badhees-200 rounded-md">
                          <button
                            type="button"
                            onClick={() => updateQuantity(item.product.id, Math.max(1, item.quantity - 1))}
                            className="w-10 h-full flex items-center justify-center text-badhees-600 hover:text-badhees-800 transition-colors"
                            aria-label="Decrease quantity"
                            disabled={item.quantity <= 1}
                          >
                            <Minus className="h-4 w-4" />
                          </button>
                          <input
                            type="number"
                            value={item.quantity}
                            onChange={(e) => updateQuantity(item.product.id, parseInt(e.target.value) || 1)}
                            className="w-12 h-full text-center border-0 focus:outline-none focus:ring-0 text-badhees-800 text-base"
                            min="1"
                            aria-label="Quantity"
                            title="Quantity"
                          />
                          <button
                            type="button"
                            onClick={() => updateQuantity(item.product.id, item.quantity + 1)}
                            className="w-10 h-full flex items-center justify-center text-badhees-600 hover:text-badhees-800 transition-colors"
                            aria-label="Increase quantity"
                          >
                            <Plus className="h-4 w-4" />
                          </button>
                        </div>
                      </div>

                      {/* Total - Desktop only */}
                      <div className="hidden md:block md:col-span-2 text-right">
                        <div className="font-medium text-badhees-800">
                          ₹{((item.product.isSale && item.product.salePrice ? item.product.salePrice : item.product.price) * item.quantity).toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="mt-4 md:mt-6 flex flex-wrap gap-4">
              <Link to="/products" className="text-sm flex items-center text-badhees-600 hover:text-badhees-accent">
                <ArrowRight className="h-3 w-3 mr-2 rotate-180" />
                Continue Shopping
              </Link>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl overflow-hidden shadow-sm border border-badhees-100 sticky top-24 md:top-28">
              <div className="p-4 md:p-6">
                <h2 className="text-lg font-bold text-badhees-800 mb-4">Order Summary</h2>

                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-badhees-600">Subtotal</span>
                    <span className="text-badhees-800 font-medium">₹{subtotal.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-badhees-600">Shipping</span>
                    <span className="text-badhees-800 font-medium">Free</span>
                  </div>
                  <div className="pt-3 mt-3 border-t border-badhees-100">
                    <div className="flex justify-between">
                      <span className="text-badhees-800 font-medium">Total</span>
                      <span className="text-lg text-badhees-800 font-bold">₹{total.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <Button
                    className="w-full flex items-center justify-center py-6 text-base"
                    onClick={handleCheckout}
                    size="lg"
                  >
                    <CreditCard className="h-5 w-5 mr-2" />
                    Proceed to Checkout
                  </Button>
                </div>

                <div className="mt-6 pt-6 border-t border-badhees-100">
                  <div className="text-xs text-badhees-500">
                    <p className="mb-2">We accept:</p>
                    <div className="flex flex-wrap items-center gap-2">
                      {["Visa", "Mastercard", "American Express", "PayPal"].map((method) => (
                        <span
                          key={method}
                          className="px-2 py-1 bg-badhees-50 rounded text-badhees-600 text-xs"
                        >
                          {method}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </PageContainer>
    </ResponsiveLayout>
  );
};

export default Cart;
