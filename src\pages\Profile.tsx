
import React, { useEffect, useState, FormEvent, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { User, ShoppingBag, LogOut, LayoutDashboard, Loader2, AlertCircle } from 'lucide-react';
import { useAuth } from '@/context/SupabaseAuthContext';
import { toast } from '@/hooks/use-toast';
import { getUserProfile, updateUserProfile, UserProfile as IUserProfile } from '@/services/userProfileService';
import { directUpdateUserProfile, ensureUserProfile } from '@/services/directDbService';
import { isValidPhone, isValidPostalCode } from '@/utils/validation';
import AvatarUpload from '@/components/profile/AvatarUpload';
import PullToRefresh from '@/components/ui/pull-to-refresh';

interface UserFormData {
  name: string;
  email: string;
  phone: string;
  dob: string;
  street: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  avatar_url?: string;
}

interface ValidationErrors {
  phone?: string;
  postal_code?: string;
}

const Profile = () => {
  const { isAuthenticated, user, logout, isAdmin } = useAuth();
  const navigate = useNavigate();
  const [formData, setFormData] = useState<UserFormData>({
    name: '',
    email: '',
    phone: '',
    dob: '',
    street: '',
    city: '',
    state: '',
    postal_code: '',
    country: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});

  // Load user data on mount
  useEffect(() => {
    window.scrollTo(0, 0);

    // Clean up any old localStorage user data
    if (localStorage.getItem('user')) {
      console.log('Removing old user data from localStorage');
      localStorage.removeItem('user');
    }

    // Load saved user profile data if it exists
    if (isAuthenticated && user) {
      setIsLoading(true);

      // Set basic user data from auth context
      setFormData(prevData => ({
        ...prevData,
        name: user.name || '',
        email: user.email || ''
      }));

      // Fetch additional profile data from Supabase
      const fetchProfileData = async () => {
        try {
          console.log('Fetching profile data for user:', user.id);
          const profileData = await getUserProfile(user.id);

          if (profileData) {
            console.log('Profile data retrieved successfully:', profileData);
            setFormData(prevData => ({
              ...prevData,
              name: profileData.display_name || user.name || '',
              email: user.email || profileData.email || '',
              phone: profileData.phone || '',
              dob: profileData.dob || '',
              street: profileData.street || '',
              city: profileData.city || '',
              state: profileData.state || '',
              postal_code: profileData.postal_code || '',
              country: profileData.country || '',
              avatar_url: profileData.avatar_url || ''
            }));
          } else {
            console.warn('No profile data returned, creating profile...');
            // If no profile data, create a new profile
            const newProfile = {
              display_name: user.name || user.email?.split('@')[0] || 'User',
              email: user.email
            };

            const success = await updateUserProfile(user.id, newProfile);

            if (success) {
              console.log('Created new profile successfully');
              // Try fetching again
              const createdProfile = await getUserProfile(user.id);

              if (createdProfile) {
                setFormData(prevData => ({
                  ...prevData,
                  name: createdProfile.display_name || user.name || '',
                  email: user.email || createdProfile.email || '',
                  phone: createdProfile.phone || '',
                  dob: createdProfile.dob || '',
                  street: createdProfile.street || '',
                  city: createdProfile.city || '',
                  state: createdProfile.state || '',
                  postal_code: createdProfile.postal_code || '',
                  country: createdProfile.country || '',
                  avatar_url: createdProfile.avatar_url || ''
                }));
              } else {
                // Use basic data from auth
                setFormData(prevData => ({
                  ...prevData,
                  name: user.name || user.email?.split('@')[0] || '',
                  email: user.email || ''
                }));
              }
            } else {
              // Use basic data from auth
              setFormData(prevData => ({
                ...prevData,
                name: user.name || user.email?.split('@')[0] || '',
                email: user.email || ''
              }));
            }
          }
        } catch (error) {
          console.error('Failed to fetch profile data:', error);
          toast({
            title: "Error loading profile",
            description: "There was a problem loading your profile data. Using basic information instead.",
            variant: "destructive"
          });

          // Use basic data from auth as fallback
          setFormData(prevData => ({
            ...prevData,
            name: user.name || user.email?.split('@')[0] || '',
            email: user.email || ''
          }));
        } finally {
          setIsLoading(false);
        }
      };

      fetchProfileData();
    }
  }, [isAuthenticated, user]);

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  // Refresh function for pull-to-refresh
  const handleRefresh = useCallback(async () => {
    if (!isAuthenticated || !user) return;

    setIsLoading(true);
    try {
      const profileData = await getUserProfile(user.id);
      if (profileData) {
        setFormData(prevData => ({
          ...prevData,
          name: profileData.display_name || user.name || '',
          email: user.email || profileData.email || '',
          phone: profileData.phone || '',
          dob: profileData.dob || '',
          street: profileData.street || '',
          city: profileData.city || '',
          state: profileData.state || '',
          postal_code: profileData.postal_code || '',
          country: profileData.country || '',
          avatar_url: profileData.avatar_url || ''
        }));
      }
      toast({
        title: 'Refreshed',
        description: 'Your profile has been updated.',
      });
    } catch (error) {
      console.error('Failed to refresh profile:', error);
      toast({
        title: "Error refreshing profile",
        description: "There was a problem refreshing your profile data.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prevData => ({
      ...prevData,
      [name]: value
    }));

    // Clear validation errors when the user changes the input
    if (name === 'phone' || name === 'postal_code') {
      setValidationErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  };

  const handleAvatarChange = (url: string | null) => {
    setFormData(prevData => ({
      ...prevData,
      avatar_url: url || undefined
    }));
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formData.name.trim() || !formData.email.trim()) {
      toast({
        title: "Validation Error",
        description: "Name and email are required fields",
        variant: "destructive"
      });
      return;
    }

    // Validate phone number and postal code
    const errors: ValidationErrors = {};

    if (formData.phone && !isValidPhone(formData.phone)) {
      errors.phone = 'Please enter a valid phone number';
    }

    if (formData.postal_code && !isValidPostalCode(formData.postal_code, formData.country)) {
      errors.postal_code = 'Please enter a valid postal code for the selected country';
    }

    // If there are validation errors, show them and don't submit
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form",
        variant: "destructive"
      });
      return;
    }

    // Save to Supabase
    if (user.id) {
      setIsSaving(true);

      try {
        console.log('Submitting profile update for user:', user.id);

        // Prepare profile data for update
        const profileData: Partial<IUserProfile> = {
          display_name: formData.name,
          email: formData.email, // Include email to ensure it's set
          phone: formData.phone,
          dob: formData.dob,
          street: formData.street,
          city: formData.city,
          state: formData.state,
          postal_code: formData.postal_code,
          country: formData.country,
          avatar_url: formData.avatar_url,
          role: 'user' // Ensure role is set
        };

        // First ensure the profile exists
        console.log('Ensuring user profile exists...');
        await ensureUserProfile(user.id);

        // Try direct database update first (using stored procedure)
        console.log('Attempting direct database update with data:', profileData);
        let success = await directUpdateUserProfile(user.id, profileData);

        // If direct update fails, fall back to regular update
        if (!success) {
          console.log('Direct update failed, falling back to regular update');
          success = await updateUserProfile(user.id, profileData);
        }

        if (success) {
          console.log('Profile updated successfully');

          // Update the user's display name in the auth context if needed
          if (user.updateUserProfile) {
            console.log('Updating user display name in auth context');
            await user.updateUserProfile({ name: formData.name, displayName: formData.name });

            toast({
              title: "Profile updated",
              description: "Your profile has been successfully updated",
            });

            // Force a refresh of the page to ensure all components show the updated name
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            console.warn('updateUserProfile function not available in auth context');

            // Refresh the page anyway to show updated data
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          }
        } else {
          console.error('Failed to update profile in Supabase');
          toast({
            title: "Error updating profile",
            description: "There was a problem updating your profile. Please try again.",
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error('Error saving profile:', error);
        toast({
          title: "Error saving profile",
          description: "There was a problem saving your profile data",
          variant: "destructive"
        });
      } finally {
        setIsSaving(false);
      }
    }
  };

  // If not authenticated, show login/register prompt
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen">
        <Navbar />
        <div className="pt-28 pb-16">
          <div className="max-w-[800px] mx-auto px-4 sm:px-8">
            <div className="bg-white rounded-xl overflow-hidden shadow-sm border border-badhees-100 p-8 text-center">
              <User className="h-16 w-16 mx-auto mb-4 text-badhees-300" />
              <h2 className="text-2xl font-bold text-badhees-800 mb-4">Sign In to Your Account</h2>
              <p className="text-badhees-600 mb-8">Please sign in to view your profile and manage your account.</p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  type="button"
                  onClick={() => navigate('/login?redirect=/profile')}
                  className="px-6 py-3 bg-badhees-accent text-white font-medium rounded-md shadow-sm hover:bg-badhees-700 transition-colors"
                >
                  Sign In
                </button>
                <button
                  type="button"
                  onClick={() => navigate('/register?redirect=/profile')}
                  className="px-6 py-3 border border-badhees-200 text-badhees-600 font-medium rounded-md shadow-sm hover:bg-badhees-50 transition-colors"
                >
                  Create Account
                </button>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  // If authenticated but no user data, show loading
  if (!user) {
    return (
      <div className="min-h-screen">
        <Navbar />
        <div className="pt-28 pb-16 flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-badhees-accent" />
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Navbar />

      <PullToRefresh onRefresh={handleRefresh} className="min-h-screen">
        <div className="pt-20 md:pt-28 pb-4 md:pb-16">
          <div className="max-w-[1200px] mx-auto px-3 sm:px-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-3 md:gap-8">
            {/* Sidebar */}
            <div className="md:col-span-1">
              <div className="bg-white rounded-xl overflow-hidden shadow-sm border border-badhees-100 p-3 md:p-6">
                <div className="flex flex-col gap-3 md:gap-4">
                  {/* Profile section - horizontal on mobile, vertical on desktop */}
                  <div className="flex md:flex-col items-start md:items-center gap-3 md:gap-2">
                    <div className="flex-shrink-0">
                      {user.id && (
                        <AvatarUpload
                          userId={user.id}
                          currentAvatarUrl={formData.avatar_url}
                          onAvatarChange={handleAvatarChange}
                          size="lg"
                        />
                      )}
                    </div>
                    <div className="flex-1 md:text-center">
                      <h2 className="text-lg font-bold text-badhees-800">{formData.name}</h2>
                      <p className="text-sm text-badhees-500">{formData.email}</p>
                    </div>
                  </div>

                  {/* Menu options - left aligned */}
                  <div className="space-y-1">
                  <button type="button" className="w-full text-left px-3 py-2 rounded-md text-badhees-800 bg-badhees-50 font-medium flex items-center justify-start">
                    <User className="mr-2 h-4 w-4" />
                    My Account
                  </button>

                  {isAdmin() && (
                    <button
                      type="button"
                      onClick={() => navigate('/admin')}
                      className="w-full text-left px-3 py-2 rounded-md text-badhees-600 hover:bg-badhees-50 flex items-center justify-start transition-colors"
                    >
                      <LayoutDashboard className="mr-2 h-4 w-4" />
                      Dashboard
                    </button>
                  )}

                  {/* Only show Orders and Wishlist for non-admin users */}
                  {!isAdmin() && (
                    <>
                      <button
                        type="button"
                        onClick={() => navigate('/orders')}
                        className="w-full text-left px-3 py-2 rounded-md text-badhees-600 hover:bg-badhees-50 flex items-center justify-start transition-colors"
                      >
                        <ShoppingBag className="mr-2 h-4 w-4" />
                        Orders
                      </button>
                    </>
                  )}
                  <button
                    type="button"
                    onClick={handleLogout}
                    className="w-full text-left px-3 py-2 rounded-md text-badhees-600 hover:bg-badhees-50 flex items-center justify-start transition-colors"
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    Logout
                  </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="md:col-span-3">
              <div className="bg-white rounded-xl overflow-hidden shadow-sm border border-badhees-100 p-3 md:p-6">
                <div className="flex justify-between items-center mb-3 md:mb-6">
                  <h2 className="text-xl font-bold text-badhees-800">Personal Information</h2>
                  {isLoading && (
                    <div className="flex items-center text-badhees-500">
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      <span>Loading...</span>
                    </div>
                  )}
                </div>

                <form className="space-y-3 md:space-y-6" onSubmit={handleSubmit}>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-badhees-700 mb-0.5">
                        Full Name
                      </label>
                      <input
                        id="name"
                        name="name"
                        type="text"
                        value={formData.name}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 rounded-md border border-badhees-200 focus:outline-none focus:ring-1 focus:ring-badhees-accent"
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-badhees-700 mb-0.5">
                        Email
                      </label>
                      <input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 rounded-md border border-badhees-200 focus:outline-none focus:ring-1 focus:ring-badhees-accent"
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-badhees-700 mb-0.5">
                        Phone Number
                      </label>
                      <input
                        id="phone"
                        name="phone"
                        type="tel"
                        value={formData.phone}
                        onChange={handleInputChange}
                        placeholder="Enter your phone number"
                        className={`w-full px-4 py-2 rounded-md border ${validationErrors.phone ? 'border-red-500' : 'border-badhees-200'} focus:outline-none focus:ring-1 focus:ring-badhees-accent`}
                      />
                      {validationErrors.phone && (
                        <div className="mt-1 text-xs text-red-500 flex items-center">
                          <AlertCircle className="h-3 w-3 mr-1" />
                          {validationErrors.phone}
                        </div>
                      )}
                    </div>
                    <div>
                      <label htmlFor="dob" className="block text-sm font-medium text-badhees-700 mb-0.5">
                        Date of Birth
                      </label>
                      <input
                        id="dob"
                        name="dob"
                        type="date"
                        value={formData.dob}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 rounded-md border border-badhees-200 focus:outline-none focus:ring-1 focus:ring-badhees-accent"
                      />
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-badhees-800 mb-2 md:mb-4">Address Information</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-6">
                      <div className="sm:col-span-2">
                        <label htmlFor="street" className="block text-sm font-medium text-badhees-700 mb-0.5">
                          Street Address
                        </label>
                        <input
                          id="street"
                          name="street"
                          type="text"
                          value={formData.street}
                          onChange={handleInputChange}
                          placeholder="Enter your street address"
                          className="w-full px-4 py-2 rounded-md border border-badhees-200 focus:outline-none focus:ring-1 focus:ring-badhees-accent"
                        />
                      </div>
                      <div>
                        <label htmlFor="city" className="block text-sm font-medium text-badhees-700 mb-0.5">
                          City
                        </label>
                        <input
                          id="city"
                          name="city"
                          type="text"
                          value={formData.city}
                          onChange={handleInputChange}
                          placeholder="Enter your city"
                          className="w-full px-4 py-2 rounded-md border border-badhees-200 focus:outline-none focus:ring-1 focus:ring-badhees-accent"
                        />
                      </div>
                      <div>
                        <label htmlFor="state" className="block text-sm font-medium text-badhees-700 mb-0.5">
                          State / Province
                        </label>
                        <input
                          id="state"
                          name="state"
                          type="text"
                          value={formData.state}
                          onChange={handleInputChange}
                          placeholder="Enter your state/province"
                          className="w-full px-4 py-2 rounded-md border border-badhees-200 focus:outline-none focus:ring-1 focus:ring-badhees-accent"
                        />
                      </div>
                      <div>
                        <label htmlFor="postal_code" className="block text-sm font-medium text-badhees-700 mb-0.5">
                          Postal Code
                        </label>
                        <input
                          id="postal_code"
                          name="postal_code"
                          type="text"
                          value={formData.postal_code}
                          onChange={handleInputChange}
                          placeholder="Enter your postal code"
                          className={`w-full px-4 py-2 rounded-md border ${validationErrors.postal_code ? 'border-red-500' : 'border-badhees-200'} focus:outline-none focus:ring-1 focus:ring-badhees-accent`}
                        />
                        {validationErrors.postal_code && (
                          <div className="mt-1 text-xs text-red-500 flex items-center">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            {validationErrors.postal_code}
                          </div>
                        )}
                      </div>
                      <div>
                        <label htmlFor="country" className="block text-sm font-medium text-badhees-700 mb-0.5">
                          Country
                        </label>
                        <select
                          id="country"
                          name="country"
                          value={formData.country}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 rounded-md border border-badhees-200 focus:outline-none focus:ring-1 focus:ring-badhees-accent"
                        >
                          <option value="">Select a country</option>
                          <option value="IN">India</option>
                          <option value="US">United States</option>
                          <option value="CA">Canada</option>
                          <option value="UK">United Kingdom</option>
                          <option value="AU">Australia</option>
                          <option value="AE">United Arab Emirates</option>
                          <option value="SG">Singapore</option>
                          <option value="MY">Malaysia</option>
                          <option value="SA">Saudi Arabia</option>
                          <option value="QA">Qatar</option>
                          <option value="KW">Kuwait</option>
                          <option value="OM">Oman</option>
                          <option value="BH">Bahrain</option>
                          <option value="JP">Japan</option>
                          <option value="KR">South Korea</option>
                          <option value="CN">China</option>
                          <option value="HK">Hong Kong</option>
                          <option value="DE">Germany</option>
                          <option value="FR">France</option>
                          <option value="IT">Italy</option>
                          <option value="ES">Spain</option>
                          <option value="NL">Netherlands</option>
                          <option value="SE">Sweden</option>
                          <option value="CH">Switzerland</option>
                          <option value="BR">Brazil</option>
                          <option value="MX">Mexico</option>
                          <option value="ZA">South Africa</option>
                          <option value="NG">Nigeria</option>
                          <option value="EG">Egypt</option>
                          <option value="TH">Thailand</option>
                          <option value="VN">Vietnam</option>
                          <option value="ID">Indonesia</option>
                          <option value="PH">Philippines</option>
                          <option value="NZ">New Zealand</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <button
                      type="submit"
                      disabled={isLoading || isSaving}
                      className="bg-badhees-800 text-white px-6 py-2 rounded-md hover:bg-badhees-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                    >
                      {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      {isSaving ? 'Saving...' : 'Save Changes'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
            </div>
          </div>
        </div>
      </PullToRefresh>

      <Footer />
    </div>
  );
};

export default Profile;
