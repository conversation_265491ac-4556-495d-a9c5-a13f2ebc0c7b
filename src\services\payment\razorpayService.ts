/**
 * Razorpay Payment Service
 *
 * This service handles Razorpay payment integration including:
 * - Creating orders
 * - Verifying payments
 * - Processing payment callbacks
 */
import Razorpay from 'razorpay';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

// Note: This service now uses API routes for server-side operations
// Frontend only needs the public key for Razorpay checkout
// Server-side operations (create order, verify payment) are handled by API routes

// Types
export interface RazorpayOrder {
  id: string;
  entity: string;
  amount: number;
  amount_paid: number;
  amount_due: number;
  currency: string;
  receipt: string;
  status: string;
  attempts: number;
  notes: any;
  created_at: number;
}

export interface RazorpayPayment {
  id: string;
  entity: string;
  amount: number;
  currency: string;
  status: string;
  order_id: string;
  method: string;
  created_at: number;
  captured: boolean;
}

export interface PaymentRecord {
  id?: string;
  order_id: string;
  razorpay_order_id: string;
  razorpay_payment_id?: string;
  amount: number;
  currency: string;
  status: 'created' | 'authorized' | 'captured' | 'failed' | 'refunded';
  method?: string;
  user_id: string;
  created_at?: string;
  updated_at?: string;
  error_description?: string;
}

/**
 * Create a new Razorpay order
 *
 * @param amount Amount in smallest currency unit (paise for INR)
 * @param currency Currency code (default: INR)
 * @param receipt Your internal order ID or receipt number
 * @param notes Additional notes for the order
 * @returns Razorpay order object
 */
export const createRazorpayOrder = async (
  amount: number,
  currency: string = 'INR',
  receipt: string,
  notes: any = {}
): Promise<RazorpayOrder> => {
  try {
    // Call the server API to create the order
    const response = await fetch('/api/razorpay/create-order', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount,
        currency,
        receipt,
        notes,
      }),
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Failed to create Razorpay order');
    }

    const order = result.data;

    // Save order record to database
    await savePaymentRecord({
      order_id: receipt,
      razorpay_order_id: order.id,
      amount: amount,
      currency,
      status: 'created',
      user_id: notes.user_id || '',
    });

    return order;
  } catch (error) {
    console.error('Error creating Razorpay order:', error);
    throw new Error(`Failed to create payment order: ${error.message}`);
  }
};

/**
 * Verify Razorpay payment signature (now handled by API route)
 * This function is deprecated - use the /api/razorpay/verify-payment endpoint instead
 */

/**
 * Save payment record to database
 *
 * @param paymentRecord Payment record to save
 * @returns Saved payment record
 */
export const savePaymentRecord = async (paymentRecord: PaymentRecord): Promise<PaymentRecord> => {
  try {
    const { data, error } = await supabase
      .from('razorpay_payments')
      .insert([{
        ...paymentRecord,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }])
      .select()
      .single();

    if (error) {
      console.error('Error saving payment record:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error saving payment record:', error);
    throw new Error(`Failed to save payment record: ${error.message}`);
  }
};

/**
 * Update payment record in database
 *
 * @param razorpayOrderId Razorpay order ID
 * @param updates Fields to update
 * @returns Updated payment record
 */
export const updatePaymentRecord = async (
  razorpayOrderId: string,
  updates: Partial<PaymentRecord>
): Promise<PaymentRecord> => {
  try {
    const { data, error } = await supabase
      .from('razorpay_payments')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('razorpay_order_id', razorpayOrderId)
      .select()
      .single();

    if (error) {
      console.error('Error updating payment record:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error updating payment record:', error);
    throw new Error(`Failed to update payment record: ${error.message}`);
  }
};

/**
 * Get payment record by Razorpay order ID
 *
 * @param razorpayOrderId Razorpay order ID
 * @returns Payment record
 */
export const getPaymentRecordByOrderId = async (razorpayOrderId: string): Promise<PaymentRecord> => {
  try {
    const { data, error } = await supabase
      .from('razorpay_payments')
      .select('*')
      .eq('razorpay_order_id', razorpayOrderId)
      .single();

    if (error) {
      console.error('Error fetching payment record:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error fetching payment record:', error);
    throw new Error(`Failed to fetch payment record: ${error.message}`);
  }
};
