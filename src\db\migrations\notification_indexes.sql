-- Notification System Performance Indexes
-- This file adds indexes to existing tables for better notification query performance

-- Add indexes for better notification query performance on existing tables

-- 1. Contact Submissions indexes
CREATE INDEX IF NOT EXISTS idx_contact_submissions_status ON contact_submissions(status);
CREATE INDEX IF NOT EXISTS idx_contact_submissions_created_at ON contact_submissions(created_at DESC);

-- 2. Customization Requests indexes  
CREATE INDEX IF NOT EXISTS idx_customization_requests_status ON customization_requests(status);
CREATE INDEX IF NOT EXISTS idx_customization_requests_created_at ON customization_requests(created_at DESC);

-- 3. Consultation Requests indexes
CREATE INDEX IF NOT EXISTS idx_consultation_requests_status ON consultation_requests(status);
CREATE INDEX IF NOT EXISTS idx_consultation_requests_created_at ON consultation_requests(created_at DESC);

-- 4. Orders indexes (if not already present)
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at DESC);

-- Add comments for documentation
COMMENT ON INDEX idx_contact_submissions_status IS 'Index for fast notification queries on contact submission status';
COMMENT ON INDEX idx_customization_requests_status IS 'Index for fast notification queries on customization request status';
COMMENT ON INDEX idx_consultation_requests_status IS 'Index for fast notification queries on consultation request status';
COMMENT ON INDEX idx_orders_status IS 'Index for fast notification queries on order status';
