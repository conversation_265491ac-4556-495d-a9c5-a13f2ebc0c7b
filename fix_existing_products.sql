-- ============================================================================
-- FIX EXISTING PRODUCTS IN DATABASE
-- This script will fix common issues with existing products
-- ============================================================================

SELECT 'FIXING EXISTING PRODUCTS IN DATABASE' as title;
SELECT '===================================' as separator;

-- ============================================================================
-- STEP 1: ENSURE REQUIRED COLUMNS EXIST
-- ============================================================================

SELECT 'STEP 1: Ensuring all required columns exist' as step;

-- Add missing columns if they don't exist
DO $$
BEGIN
  -- Check and add is_sale column
  IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'is_sale') THEN
    ALTER TABLE products ADD COLUMN is_sale BOOLEAN DEFAULT false;
    RAISE NOTICE 'Added is_sale column';
  END IF;
  
  -- Check and add is_new column
  IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'is_new') THEN
    ALTER TABLE products ADD COLUMN is_new BOOLEAN DEFAULT false;
    RAISE NOTICE 'Added is_new column';
  END IF;
  
  -- Check and add is_featured column
  IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'is_featured') THEN
    ALTER TABLE products ADD COLUMN is_featured BOOLEAN DEFAULT false;
    RAISE NOTICE 'Added is_featured column';
  END IF;
  
  -- Check and add status column
  IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'status') THEN
    ALTER TABLE products ADD COLUMN status TEXT CHECK (status IN ('active', 'draft', 'deleted')) DEFAULT 'active';
    RAISE NOTICE 'Added status column';
  END IF;
  
  -- Check and add stock column (might be named differently)
  IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'stock') THEN
    IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'stock_quantity') THEN
      ALTER TABLE products ADD COLUMN stock INTEGER DEFAULT 0;
      UPDATE products SET stock = stock_quantity WHERE stock_quantity IS NOT NULL;
      RAISE NOTICE 'Added stock column and copied from stock_quantity';
    ELSE
      ALTER TABLE products ADD COLUMN stock INTEGER DEFAULT 0;
      RAISE NOTICE 'Added stock column';
    END IF;
  END IF;
  
  -- Check and add specifications column
  IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'specifications') THEN
    ALTER TABLE products ADD COLUMN specifications JSONB DEFAULT '{}';
    RAISE NOTICE 'Added specifications column';
  END IF;
  
  -- Check and add rating columns
  IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'rating') THEN
    ALTER TABLE products ADD COLUMN rating DECIMAL(3,2) DEFAULT 0;
    RAISE NOTICE 'Added rating column';
  END IF;
  
  IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'review_count') THEN
    ALTER TABLE products ADD COLUMN review_count INTEGER DEFAULT 0;
    RAISE NOTICE 'Added review_count column';
  END IF;
END $$;

-- ============================================================================
-- STEP 2: FIX PRODUCT STATUS
-- ============================================================================

SELECT 'STEP 2: Fixing product status' as step;

-- Set all products to active if they don't have a status
UPDATE products 
SET status = 'active' 
WHERE status IS NULL OR status = '';

-- Show status update results
SELECT 
  status,
  COUNT(*) as count
FROM products 
GROUP BY status;

-- ============================================================================
-- STEP 3: FIX SALE PRICING
-- ============================================================================

SELECT 'STEP 3: Fixing sale pricing logic' as step;

-- Update is_sale flag based on sale_price
UPDATE products 
SET is_sale = CASE 
  WHEN sale_price IS NOT NULL AND sale_price > 0 AND sale_price < price THEN true
  ELSE false
END;

-- Show sale products
SELECT 'Products on sale:' as info;
SELECT 
  name,
  price,
  sale_price,
  is_sale
FROM products 
WHERE is_sale = true;

-- ============================================================================
-- STEP 4: FIX STOCK LEVELS
-- ============================================================================

SELECT 'STEP 4: Fixing stock levels' as step;

-- Set default stock for products with no stock
UPDATE products 
SET stock = 10 
WHERE stock IS NULL OR stock = 0;

-- Show stock status
SELECT 
  CASE 
    WHEN stock > 10 THEN 'High Stock'
    WHEN stock > 0 THEN 'Low Stock'
    ELSE 'Out of Stock'
  END as stock_level,
  COUNT(*) as product_count
FROM products 
GROUP BY 
  CASE 
    WHEN stock > 10 THEN 'High Stock'
    WHEN stock > 0 THEN 'Low Stock'
    ELSE 'Out of Stock'
  END;

-- ============================================================================
-- STEP 5: ENSURE CATEGORIES EXIST
-- ============================================================================

SELECT 'STEP 5: Ensuring categories exist' as step;

-- Create default categories if none exist
INSERT INTO categories (id, name, description, is_active) 
SELECT 
  uuid_generate_v4(),
  category_name,
  'Default category for ' || category_name,
  true
FROM (
  VALUES 
    ('Furniture'),
    ('Chairs'),
    ('Tables'),
    ('Storage'),
    ('Lighting'),
    ('Decor')
) AS default_categories(category_name)
WHERE NOT EXISTS (SELECT 1 FROM categories WHERE name = category_name)
ON CONFLICT (name) DO NOTHING;

-- Assign products to default category if they don't have one
DO $$
DECLARE
  default_category_id UUID;
BEGIN
  -- Get or create a default category
  SELECT id INTO default_category_id FROM categories LIMIT 1;
  
  IF default_category_id IS NOT NULL THEN
    UPDATE products 
    SET category_id = default_category_id 
    WHERE category_id IS NULL 
       OR NOT EXISTS (SELECT 1 FROM categories WHERE id = products.category_id);
    
    RAISE NOTICE 'Assigned products to default category: %', default_category_id;
  END IF;
END $$;

-- ============================================================================
-- STEP 6: CREATE PRODUCT IMAGES TABLE IF NEEDED
-- ============================================================================

SELECT 'STEP 6: Ensuring product_images table exists' as step;

-- Create product_images table if it doesn't exist
CREATE TABLE IF NOT EXISTS product_images (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  image_url TEXT NOT NULL,
  alt_text TEXT,
  is_primary BOOLEAN DEFAULT false,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- If products have an 'images' JSONB column, migrate to product_images table
DO $$
DECLARE
  product_record RECORD;
  image_url TEXT;
  image_index INTEGER;
BEGIN
  IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'images') THEN
    FOR product_record IN SELECT id, images FROM products WHERE images IS NOT NULL AND jsonb_array_length(images) > 0 LOOP
      image_index := 0;
      FOR image_url IN SELECT jsonb_array_elements_text(product_record.images) LOOP
        INSERT INTO product_images (product_id, image_url, alt_text, is_primary, sort_order)
        VALUES (
          product_record.id,
          image_url,
          'Product image',
          image_index = 0, -- First image is primary
          image_index
        )
        ON CONFLICT DO NOTHING;
        image_index := image_index + 1;
      END LOOP;
    END LOOP;
    RAISE NOTICE 'Migrated images from JSONB to product_images table';
  END IF;
END $$;

-- Add default images for products without any
INSERT INTO product_images (product_id, image_url, alt_text, is_primary, sort_order)
SELECT 
  p.id,
  'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
  p.name || ' - Default Image',
  true,
  0
FROM products p
WHERE NOT EXISTS (SELECT 1 FROM product_images WHERE product_id = p.id)
ON CONFLICT DO NOTHING;

-- ============================================================================
-- STEP 7: VERIFICATION
-- ============================================================================

SELECT 'STEP 7: Final verification' as step;

-- Show final product status
SELECT 
  'Total products' as metric,
  COUNT(*) as value
FROM products
UNION ALL
SELECT 
  'Active products' as metric,
  COUNT(*) as value
FROM products WHERE status = 'active'
UNION ALL
SELECT 
  'Products with images' as metric,
  COUNT(DISTINCT product_id) as value
FROM product_images
UNION ALL
SELECT 
  'Products with categories' as metric,
  COUNT(*) as value
FROM products p 
JOIN categories c ON p.category_id = c.id;

-- Show sample of fixed products
SELECT 'Sample of fixed products:' as info;
SELECT 
  p.id,
  p.name,
  p.price,
  p.sale_price,
  p.is_sale,
  p.status,
  p.stock,
  c.name as category,
  CASE 
    WHEN pi.product_id IS NOT NULL THEN 'Has Images'
    ELSE 'No Images'
  END as image_status
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = true
WHERE p.status = 'active'
ORDER BY p.name
LIMIT 5;

SELECT 'PRODUCT FIXES COMPLETE!' as result;
SELECT 'Your existing products should now be properly configured and visible.' as message;
