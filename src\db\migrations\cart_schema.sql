-- Create cart_items table to store user cart items
CREATE TABLE IF NOT EXISTS cart_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  quantity INTEGER NOT NULL CHECK (quantity > 0),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Add a unique constraint to prevent duplicate products in cart
  UNIQUE(user_id, product_id)
);

-- Create RLS policies for cart_items
-- Allow users to view their own cart items
CREATE POLICY "Users can view their own cart items"
ON cart_items
FOR SELECT
USING (user_id = auth.uid());

-- Allow users to insert their own cart items
CREATE POLICY "Users can insert their own cart items"
ON cart_items
FOR INSERT
WITH CHECK (user_id = auth.uid());

-- Allow users to update their own cart items
CREATE POLICY "Users can update their own cart items"
ON cart_items
FOR UPDATE
USING (user_id = auth.uid());

-- Allow users to delete their own cart items
CREATE POLICY "Users can delete their own cart items"
ON cart_items
FOR DELETE
USING (user_id = auth.uid());

-- Enable RLS on cart_items
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;

-- Create a function to get cart items with product details
CREATE OR REPLACE FUNCTION get_cart_items(p_user_id UUID)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  product_id UUID,
  quantity INTEGER,
  product_name TEXT,
  product_price DECIMAL(10, 2),
  product_sale_price DECIMAL(10, 2),
  product_image TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ci.id,
    ci.user_id,
    ci.product_id,
    ci.quantity,
    p.name AS product_name,
    p.price AS product_price,
    p.sale_price AS product_sale_price,
    (
      SELECT pi.image_url 
      FROM product_images pi 
      WHERE pi.product_id = p.id AND pi.is_primary = true
      LIMIT 1
    ) AS product_image,
    ci.created_at,
    ci.updated_at
  FROM 
    cart_items ci
  JOIN 
    products p ON ci.product_id = p.id
  WHERE 
    ci.user_id = p_user_id;
END;
$$;

-- Create a function to add an item to the cart
CREATE OR REPLACE FUNCTION add_to_cart(
  p_user_id UUID,
  p_product_id UUID,
  p_quantity INTEGER
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_cart_item_id UUID;
BEGIN
  -- Check if the product exists in the cart
  SELECT id INTO v_cart_item_id
  FROM cart_items
  WHERE user_id = p_user_id AND product_id = p_product_id;
  
  IF v_cart_item_id IS NOT NULL THEN
    -- Update quantity if product already exists
    UPDATE cart_items
    SET quantity = quantity + p_quantity,
        updated_at = NOW()
    WHERE id = v_cart_item_id;
    
    RETURN v_cart_item_id;
  ELSE
    -- Insert new cart item
    INSERT INTO cart_items (user_id, product_id, quantity)
    VALUES (p_user_id, p_product_id, p_quantity)
    RETURNING id INTO v_cart_item_id;
    
    RETURN v_cart_item_id;
  END IF;
END;
$$;

-- Create a function to update cart item quantity
CREATE OR REPLACE FUNCTION update_cart_quantity(
  p_user_id UUID,
  p_product_id UUID,
  p_quantity INTEGER
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF p_quantity <= 0 THEN
    -- Remove item if quantity is 0 or negative
    DELETE FROM cart_items
    WHERE user_id = p_user_id AND product_id = p_product_id;
  ELSE
    -- Update quantity
    UPDATE cart_items
    SET quantity = p_quantity,
        updated_at = NOW()
    WHERE user_id = p_user_id AND product_id = p_product_id;
  END IF;
  
  RETURN TRUE;
END;
$$;

-- Create a function to clear the cart
CREATE OR REPLACE FUNCTION clear_cart(p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  DELETE FROM cart_items
  WHERE user_id = p_user_id;
  
  RETURN TRUE;
END;
$$;

-- Add comments to explain the purpose of each function
COMMENT ON FUNCTION get_cart_items IS 'Gets all cart items for a user with product details';
COMMENT ON FUNCTION add_to_cart IS 'Adds a product to the cart or updates quantity if it already exists';
COMMENT ON FUNCTION update_cart_quantity IS 'Updates the quantity of a product in the cart, removes if quantity <= 0';
COMMENT ON FUNCTION clear_cart IS 'Removes all items from a user''s cart';
