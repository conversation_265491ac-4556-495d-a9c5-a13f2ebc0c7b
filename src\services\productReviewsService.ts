import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';

export interface ProductReview {
  id: string;
  productId: string;
  userId: string;
  rating: number;
  comment?: string;
  title?: string;
  createdAt: string;
  userName?: string;
}

export interface PurchasableProduct {
  productId: string;
  productName: string;
  orderId: string;
  purchaseDate: string;
  hasReviewed: boolean;
}

interface SupabaseProductReview {
  id: string;
  product_id: string;
  user_id: string;
  rating: number;
  comment: string | null;
  title: string | null;
  created_at: string;
  updated_at: string;
  user_profiles?: {
    display_name: string | null;
    first_name: string | null;
    last_name: string | null;
  };
}

export interface ProductRatingSummary {
  averageRating: number;
  reviewCount: number;
}

// REMOVED: RatingUpdateCallback type - no longer needed without real-time subscriptions

// Map Supabase review to frontend format
const mapSupabaseReviewToFrontend = (review: SupabaseProductReview): ProductReview => {
  // Create a display name from available user information
  let userName = 'Anonymous';
  if (review.user_profiles) {
    if (review.user_profiles.display_name) {
      userName = review.user_profiles.display_name;
    } else if (review.user_profiles.first_name && review.user_profiles.last_name) {
      userName = `${review.user_profiles.first_name} ${review.user_profiles.last_name}`;
    } else if (review.user_profiles.first_name) {
      userName = review.user_profiles.first_name;
    }
  }

  return {
    id: review.id,
    productId: review.product_id,
    userId: review.user_id,
    rating: review.rating,
    comment: review.comment || undefined,
    title: review.title || undefined,
    createdAt: review.created_at,
    userName
  };
};

// Get product rating summary (average rating and review count)
export const getProductRatingSummary = async (productId: string): Promise<ProductRatingSummary> => {
  try {
    console.log(`[ProductRating] Fetching rating summary for product: ${productId}`);

    // Method 1: Try the RPC function first (most reliable)
    try {
      const { data: rpcData, error: rpcError } = await supabase
        .rpc('get_product_rating', { input_product_id: productId });

      if (!rpcError && rpcData) {
        const result = {
          averageRating: parseFloat(rpcData.average_rating) || 0,
          reviewCount: parseInt(rpcData.review_count) || 0
        };
        console.log(`[ProductRating] Found rating summary via RPC for ${productId}:`, result);
        return result;
      }

      if (rpcError) {
        console.log(`[ProductRating] RPC query error for ${productId}:`, {
          code: rpcError.code,
          message: rpcError.message
        });
      }
    } catch (rpcException) {
      console.log(`[ProductRating] Exception with RPC query, trying view:`, rpcException);
    }

    // Method 2: Try the product_ratings_summary view
    try {
      const { data: viewData, error: viewError } = await supabase
        .from('product_ratings_summary')
        .select('*')
        .eq('product_id', productId)
        .single();

      if (!viewError && viewData) {
        const result = {
          averageRating: parseFloat(viewData.average_rating) || 0,
          reviewCount: parseInt(viewData.review_count) || 0
        };
        console.log(`[ProductRating] Found rating summary via view for ${productId}:`, result);
        return result;
      }

      // Log specific error types for debugging
      if (viewError) {
        console.log(`[ProductRating] View query error for ${productId}:`, {
          code: viewError.code,
          message: viewError.message,
          details: viewError.details
        });

        // If it's a 406 error or permission issue, fall back immediately
        if (viewError.code === 'PGRST301' || viewError.message?.includes('406') || viewError.message?.includes('Not Acceptable')) {
          console.log(`[ProductRating] 406/Permission error detected, using direct calculation`);
        }
      }
    } catch (viewException) {
      console.log(`[ProductRating] Exception with view query, using direct calculation:`, viewException);
    }

    // Method 3: Fallback to direct calculation if both RPC and view fail
    console.log(`[ProductRating] Using direct calculation for product rating summary: ${productId}`);
    const { data: reviewsData, error: reviewsError } = await supabase
      .from('product_reviews')
      .select('rating')
      .eq('product_id', productId);

    if (reviewsError) {
      console.error(`[ProductRating] Error in direct calculation for ${productId}:`, reviewsError);
      // Don't throw error, return default values instead
      return { averageRating: 0, reviewCount: 0 };
    }

    if (!reviewsData || reviewsData.length === 0) {
      console.log(`[ProductRating] No reviews found for product ${productId}`);
      return { averageRating: 0, reviewCount: 0 };
    }

    const totalRating = reviewsData.reduce((sum, review) => sum + review.rating, 0);
    const averageRating = Math.round((totalRating / reviewsData.length) * 10) / 10; // Round to 1 decimal

    const result = {
      averageRating: averageRating,
      reviewCount: reviewsData.length
    };

    console.log(`[ProductRating] Calculated rating summary for ${productId}:`, result);
    return result;
  } catch (error: any) {
    console.error(`[ProductRating] Unexpected error fetching rating summary for ${productId}:`, error);
    // Always return default values instead of throwing
    return { averageRating: 0, reviewCount: 0 };
  }
};

// Get all reviews for a product
export const getProductReviews = async (productId: string): Promise<ProductReview[]> => {
  try {
    console.log(`Fetching reviews for product ${productId}`);

    // Validate productId
    if (!productId || productId.trim() === '') {
      console.warn('Invalid productId provided to getProductReviews');
      return [];
    }

    // First try with the join approach
    try {
      const { data: reviewsData, error: reviewsError } = await supabase
        .from('product_reviews')
        .select(`
          *,
          user_profiles(display_name, first_name, last_name)
        `)
        .eq('product_id', productId)
        .order('created_at', { ascending: false });

      if (!reviewsError && reviewsData && Array.isArray(reviewsData)) {
        console.log(`Found ${reviewsData.length} reviews for product ${productId} using join`);
        return (reviewsData as SupabaseProductReview[]).map(mapSupabaseReviewToFrontend);
      }

      // If there's an error with the join, log it and fall back to separate queries
      console.warn('Error with join query, falling back to separate queries:', reviewsError);
    } catch (joinError) {
      console.warn('Exception with join query, falling back to separate queries:', joinError);
    }

    // Fallback: Get reviews without the join
    const { data: reviewsData, error: reviewsError } = await supabase
      .from('product_reviews')
      .select('*')
      .eq('product_id', productId)
      .order('created_at', { ascending: false });

    if (reviewsError) {
      console.error('Error fetching reviews (fallback):', reviewsError);
      // Don't throw error, return empty array instead
      console.warn('Returning empty array due to database error');
      return [];
    }

    if (!reviewsData || reviewsData.length === 0) {
      console.log(`No reviews found for product ${productId}`);
      return [];
    }

    console.log(`Found ${reviewsData.length} reviews for product ${productId} using fallback`);

    // For each review, get the user profile separately
    const reviewsWithUserProfiles = await Promise.all(
      reviewsData.map(async (review) => {
        if (!review.user_id) {
          return { ...review, user_profiles: null };
        }

        try {
          const { data: userData, error: userError } = await supabase
            .from('user_profiles')
            .select('display_name, first_name, last_name')
            .eq('id', review.user_id)
            .single();

          if (userError && userError.code !== 'PGRST116') {
            console.warn(`Error fetching user profile for user ${review.user_id}:`, userError);
          }

          return {
            ...review,
            user_profiles: userData || null
          };
        } catch (error) {
          console.warn(`Exception fetching user profile for user ${review.user_id}:`, error);
          return { ...review, user_profiles: null };
        }
      })
    );

    // Map the reviews to frontend format
    return (reviewsWithUserProfiles as SupabaseProductReview[]).map(mapSupabaseReviewToFrontend);
  } catch (error: any) {
    console.error(`Error fetching reviews for product ${productId}:`, error);
    // Don't show toast error for reviews fetching as it's not critical
    // Just log the error and return empty array
    console.warn('Returning empty reviews array due to unexpected error');
    return [];
  }
};

// Check if user has already reviewed a product
export const hasUserReviewedProduct = async (productId: string, userId: string): Promise<boolean> => {
  try {
    const { data, error, count } = await supabase
      .from('product_reviews')
      .select('id', { count: 'exact' })
      .eq('product_id', productId)
      .eq('user_id', userId);

    if (error) {
      throw error;
    }

    return count !== null && count > 0;
  } catch (error: any) {
    console.error(`Error checking if user has reviewed product ${productId}:`, error);
    return false;
  }
};

// Check if user has purchased a product
export const hasUserPurchasedProduct = async (productId: string, userId: string): Promise<boolean> => {
  try {
    console.log(`Checking if user ${userId} has purchased product ${productId}`);

    // Validate inputs
    if (!productId || !userId) {
      console.warn('Invalid productId or userId provided to hasUserPurchasedProduct');
      return false;
    }

    // First try using the RPC function
    try {
      const { data: rpcData, error: rpcError } = await supabase
        .rpc('has_user_purchased_product', {
          input_user_id: userId,
          input_product_id: productId
        });

      if (!rpcError) {
        console.log(`RPC result for user purchase check: ${rpcData}`);
        return rpcData || false;
      }

      // If RPC fails, fall back to direct query
      console.log('RPC failed, falling back to direct query:', rpcError);
    } catch (rpcError) {
      console.warn('RPC exception, falling back to direct query:', rpcError);
    }

    const { data: orderData, error: orderError } = await supabase
      .from('orders')
      .select(`
        id,
        order_items!inner(product_id)
      `)
      .eq('user_id', userId)
      .eq('order_items.product_id', productId)
      .in('status', ['delivered', 'shipped'])
      .limit(1);

    if (orderError) {
      console.error('Error in fallback query:', orderError);
      throw orderError;
    }

    const hasPurchased = orderData && orderData.length > 0;
    console.log(`Direct query result for user purchase check: ${hasPurchased}`);

    return hasPurchased;
  } catch (error: any) {
    console.error(`Error checking if user has purchased product ${productId}:`, error);
    return false;
  }
};

// Get products that a user has purchased and can review
export const getUserPurchasableProducts = async (userId: string): Promise<PurchasableProduct[]> => {
  try {
    console.log(`Fetching purchasable products for user ${userId}`);

    // First try using the view
    const { data: viewData, error: viewError } = await supabase
      .from('user_purchasable_reviews')
      .select('*')
      .eq('user_id', userId);

    if (!viewError && viewData) {
      console.log(`Found ${viewData.length} purchasable products for user ${userId} using view`);
      return (viewData || []).map(item => ({
        productId: item.product_id,
        productName: item.product_name,
        orderId: item.order_id,
        purchaseDate: item.purchase_date,
        hasReviewed: item.has_reviewed
      }));
    }

    // If view doesn't exist or has an error, fall back to direct query
    console.log('View query failed, falling back to direct query:', viewError);

    // Get all delivered/shipped orders for the user
    const { data: ordersData, error: ordersError } = await supabase
      .from('orders')
      .select(`
        id,
        created_at,
        order_items:order_items(
          product_id,
          products:products(id, name)
        )
      `)
      .eq('user_id', userId)
      .in('status', ['delivered', 'shipped']);

    if (ordersError) {
      console.error('Error fetching orders:', ordersError);
      throw ordersError;
    }

    if (!ordersData || ordersData.length === 0) {
      console.log(`No orders found for user ${userId}`);
      return [];
    }

    // Get all reviews by the user
    const { data: reviewsData, error: reviewsError } = await supabase
      .from('product_reviews')
      .select('product_id')
      .eq('user_id', userId);

    if (reviewsError) {
      console.error('Error fetching user reviews:', reviewsError);
      throw reviewsError;
    }

    // Create a set of product IDs that the user has already reviewed
    const reviewedProductIds = new Set((reviewsData || []).map(review => review.product_id));

    // Flatten the orders data to get all purchased products
    const purchasableProducts: PurchasableProduct[] = [];

    ordersData.forEach(order => {
      if (order.order_items && Array.isArray(order.order_items)) {
        order.order_items.forEach(item => {
          if (item.products) {
            const productId = item.product_id;
            const productName = item.products.name;

            purchasableProducts.push({
              productId,
              productName,
              orderId: order.id,
              purchaseDate: order.created_at,
              hasReviewed: reviewedProductIds.has(productId)
            });
          }
        });
      }
    });

    console.log(`Found ${purchasableProducts.length} purchasable products for user ${userId} using direct query`);
    return purchasableProducts;
  } catch (error: any) {
    console.error(`Error fetching purchasable products for user ${userId}:`, error);
    return [];
  }
};

// Add a new review
export const addProductReview = async (
  productId: string,
  rating: number,
  comment?: string,
  title?: string
): Promise<ProductReview | null> => {
  try {
    console.log(`Adding review for product ${productId} with rating ${rating}`);

    // Get current user
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      console.error('User not authenticated');
      toast({
        title: 'Authentication required',
        description: 'You must be logged in to leave a review',
        variant: 'destructive',
      });
      return null;
    }

    console.log(`User authenticated: ${user.id}`);

    // Check if user has already reviewed this product
    const hasReviewed = await hasUserReviewedProduct(productId, user.id);
    if (hasReviewed) {
      console.log(`User ${user.id} has already reviewed product ${productId}`);
      toast({
        title: 'Review already exists',
        description: 'You have already reviewed this product. You can edit your existing review.',
        variant: 'destructive',
      });
      return null;
    }

    // Check if user has purchased this product
    const hasPurchased = await hasUserPurchasedProduct(productId, user.id);
    console.log(`User ${user.id} has purchased product ${productId}: ${hasPurchased}`);

    if (!hasPurchased) {
      toast({
        title: 'Purchase required',
        description: 'You can only review products you have purchased.',
        variant: 'destructive',
      });
      return null;
    }

    // Validate rating
    if (rating < 1 || rating > 5) {
      console.error(`Invalid rating: ${rating}`);
      toast({
        title: 'Invalid rating',
        description: 'Rating must be between 1 and 5 stars',
        variant: 'destructive',
      });
      return null;
    }

    // Insert the review
    console.log('Inserting review into database');
    const { data, error } = await supabase
      .from('product_reviews')
      .insert({
        product_id: productId,
        user_id: user.id,
        rating,
        comment,
        title,
      })
      .select('*')
      .single();

    if (error) {
      console.error('Error inserting review:', error);
      throw error;
    }

    console.log('Review inserted successfully:', data);

    // Get user profile data separately
    let userData = null;
    try {
      const { data: profileData, error: profileError } = await supabase
        .from('user_profiles')
        .select('display_name, first_name, last_name')
        .eq('id', user.id)
        .single();

      if (!profileError) {
        userData = profileData;
      } else {
        console.warn('Error fetching user profile data:', profileError);
      }
    } catch (profileError) {
      console.warn('Exception fetching user profile data:', profileError);
    }

    // Try to get the review with user data in a single query
    let reviewWithUserData = null;
    try {
      const { data: joinData, error: joinError } = await supabase
        .from('product_reviews')
        .select(`
          *,
          user_profiles:user_profiles(display_name, first_name, last_name)
        `)
        .eq('id', data.id)
        .single();

      if (!joinError) {
        reviewWithUserData = joinData;
      } else {
        console.warn('Error fetching review with user data:', joinError);
      }
    } catch (joinError) {
      console.warn('Exception fetching review with user data:', joinError);
    }

    toast({
      title: 'Review submitted',
      description: 'Thank you for your feedback!',
    });

    // Use the joined data if available, otherwise use the original data with separately fetched user profile
    const finalReviewData = reviewWithUserData || {
      ...data,
      user_profiles: userData
    };

    return mapSupabaseReviewToFrontend(finalReviewData as SupabaseProductReview);
  } catch (error: any) {
    console.error(`Error adding review for product ${productId}:`, error);
    toast({
      title: 'Error submitting review',
      description: error.message || 'An unexpected error occurred',
      variant: 'destructive',
    });
    return null;
  }
};

// Update an existing review
export const updateProductReview = async (
  reviewId: string,
  rating: number,
  comment?: string,
  title?: string
): Promise<ProductReview | null> => {
  try {
    // First, get the review to get the user_id
    const { data: reviewData, error: reviewError } = await supabase
      .from('product_reviews')
      .select('user_id')
      .eq('id', reviewId)
      .single();

    if (reviewError) {
      throw reviewError;
    }

    // Update the review
    const { data, error } = await supabase
      .from('product_reviews')
      .update({
        rating,
        comment,
        title,
        updated_at: new Date().toISOString(),
      })
      .eq('id', reviewId)
      .select('*')
      .single();

    if (error) {
      throw error;
    }

    // Get user profile data separately
    let userData = null;
    try {
      const { data: profileData, error: profileError } = await supabase
        .from('user_profiles')
        .select('display_name, first_name, last_name')
        .eq('id', reviewData.user_id)
        .single();

      if (!profileError) {
        userData = profileData;
      } else if (profileError.code !== 'PGRST116') {
        console.warn(`Error fetching user profile for user ${reviewData.user_id}:`, profileError);
      }
    } catch (profileError) {
      console.warn(`Exception fetching user profile for user ${reviewData.user_id}:`, profileError);
    }

    // Try to get the review with user data in a single query
    let reviewWithUserData = null;
    try {
      const { data: joinData, error: joinError } = await supabase
        .from('product_reviews')
        .select(`
          *,
          user_profiles:user_profiles(display_name, first_name, last_name)
        `)
        .eq('id', reviewId)
        .single();

      if (!joinError) {
        reviewWithUserData = joinData;
        console.log('Successfully fetched review with user data in a single query');
      } else {
        console.warn('Error fetching review with user data:', joinError);
      }
    } catch (joinError) {
      console.warn('Exception fetching review with user data:', joinError);
    }

    // Combine the data - use joined data if available, otherwise use separate queries
    const finalReviewData = reviewWithUserData || {
      ...data,
      user_profiles: userData
    };

    toast({
      title: 'Review updated',
      description: 'Your review has been updated successfully',
    });

    return mapSupabaseReviewToFrontend(reviewWithUserData as SupabaseProductReview);
  } catch (error: any) {
    console.error(`Error updating review ${reviewId}:`, error);
    toast({
      title: 'Error updating review',
      description: error.message || 'An unexpected error occurred',
      variant: 'destructive',
    });
    return null;
  }
};

// Delete a review
export const deleteProductReview = async (reviewId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('product_reviews')
      .delete()
      .eq('id', reviewId);

    if (error) {
      throw error;
    }

    toast({
      title: 'Review deleted',
      description: 'Your review has been deleted successfully',
    });

    return true;
  } catch (error: any) {
    console.error(`Error deleting review ${reviewId}:`, error);
    toast({
      title: 'Error deleting review',
      description: error.message || 'An unexpected error occurred',
      variant: 'destructive',
    });
    return false;
  }
};

// Get user's review for a product
export const getUserReviewForProduct = async (productId: string, userId: string): Promise<ProductReview | null> => {
  try {
    console.log(`Fetching user review for product ${productId} by user ${userId}`);

    // First try with the join approach
    try {
      const { data: reviewData, error: reviewError } = await supabase
        .from('product_reviews')
        .select(`
          *,
          user_profiles:user_profiles(display_name, first_name, last_name)
        `)
        .eq('product_id', productId)
        .eq('user_id', userId)
        .single();

      if (!reviewError && reviewData) {
        console.log('Successfully fetched review with user data in a single query');
        return mapSupabaseReviewToFrontend(reviewData as SupabaseProductReview);
      }

      // If there's an error but it's just "not found", return null
      if (reviewError && reviewError.code === 'PGRST116') {
        console.log('No review found for this product and user');
        return null;
      }

      // If there's another error with the join, log it and fall back to separate queries
      console.warn('Error with join query, falling back to separate queries:', reviewError);
    } catch (joinError) {
      console.warn('Exception with join query, falling back to separate queries:', joinError);
    }

    // Fallback: Get the review without the join
    const { data, error } = await supabase
      .from('product_reviews')
      .select('*')
      .eq('product_id', productId)
      .eq('user_id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No review found (not an error)
        console.log('No review found for this product and user (fallback)');
        return null;
      }
      console.error('Error fetching review (fallback):', error);
      throw error;
    }

    console.log('Found review using fallback query');

    // Get user profile data separately
    let userData = null;
    try {
      const { data: profileData, error: profileError } = await supabase
        .from('user_profiles')
        .select('display_name, first_name, last_name')
        .eq('id', userId)
        .single();

      if (!profileError) {
        userData = profileData;
      } else if (profileError.code !== 'PGRST116') {
        console.warn(`Error fetching user profile for user ${userId}:`, profileError);
      }
    } catch (profileError) {
      console.warn(`Exception fetching user profile for user ${userId}:`, profileError);
    }

    // Combine the data
    const reviewWithUserData = {
      ...data,
      user_profiles: userData
    };

    return mapSupabaseReviewToFrontend(reviewWithUserData as SupabaseProductReview);
  } catch (error: any) {
    console.error(`Error fetching user review for product ${productId}:`, error);
    return null;
  }
};

// REMOVED: Real-time subscription functionality that was causing infinite loops
// If real-time updates are needed in the future, they should be implemented
// with a different approach that doesn't cause infinite API calls

// Invalidate React Query cache for product ratings - OPTIMIZED to prevent loops
export const invalidateProductRatingCache = (queryClient: any, productId: string) => {
  // Only invalidate specific product queries, not general lists to prevent infinite loops
  queryClient.invalidateQueries(['product-reviews', productId]);

  // Don't invalidate the rating cache as we update it directly in the subscription
  // Don't invalidate general product lists as they cause unnecessary re-fetches
};