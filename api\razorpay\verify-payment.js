import crypto from 'crypto';

export default async (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version');

  // Handle OPTIONS request
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  try {
    console.log('Received payment verification request:', req.body);

    const {
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature,
      order_id
    } = req.body;

    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
      console.log('Missing required parameters');
      return res.status(400).json({
        success: false,
        error: 'Payment verification failed: Missing required parameters'
      });
    }

    // Validate environment variables
    if (!process.env.RAZORPAY_SECRET) {
      throw new Error('Missing RAZORPAY_SECRET environment variable');
    }

    // Get the secret key from environment variables
    const secret = process.env.RAZORPAY_SECRET;

    // Create signature
    const shasum = crypto.createHmac('sha256', secret);
    shasum.update(`${razorpay_order_id}|${razorpay_payment_id}`);
    const digest = shasum.digest('hex');

    console.log('Generated signature:', digest);
    console.log('Received signature:', razorpay_signature);

    // Verify signature
    if (digest !== razorpay_signature) {
      console.log('Invalid signature');
      return res.status(400).json({
        success: false,
        error: 'Payment verification failed: Invalid signature'
      });
    }

    console.log('Payment verified successfully');
    return res.status(200).json({
      success: true,
      message: 'Payment verified successfully',
      order_id: order_id,
      payment_id: razorpay_payment_id
    });
  } catch (error) {
    console.error('Error verifying payment:', error);
    return res.status(500).json({
      success: false,
      error: error.message || 'Failed to verify payment'
    });
  }
};
