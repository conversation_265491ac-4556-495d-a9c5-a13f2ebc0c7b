-- Create product_reviews table
CREATE TABLE product_reviews (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id),
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  comment TEXT,
  title TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add index for faster lookups
CREATE INDEX idx_product_reviews_product_id ON product_reviews(product_id);
CREATE INDEX idx_product_reviews_user_id ON product_reviews(user_id);

-- Set up Row Level Security (RLS)
ALTER TABLE product_reviews ENABLE ROW LEVEL SECURITY;

-- Create policies for product_reviews
-- Anyone can view reviews
CREATE POLICY "Anyone can view product reviews" 
  ON product_reviews FOR SELECT 
  USING (true);

-- Users can insert their own reviews
CREATE POLICY "Users can insert their own reviews" 
  ON product_reviews FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Users can update their own reviews
CREATE POLICY "Users can update their own reviews" 
  ON product_reviews FOR UPDATE 
  USING (auth.uid() = user_id);

-- Users can delete their own reviews
CREATE POLICY "Users can delete their own reviews" 
  ON product_reviews FOR DELETE 
  USING (auth.uid() = user_id);

-- Admins can manage all reviews
CREATE POLICY "Admins can manage all reviews" 
  ON product_reviews FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Create a function to calculate average rating for a product
CREATE OR REPLACE FUNCTION get_product_average_rating(product_id UUID)
RETURNS TABLE (average_rating NUMERIC, review_count BIGINT) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COALESCE(AVG(rating)::NUMERIC, 0) AS average_rating,
    COUNT(*) AS review_count
  FROM product_reviews
  WHERE product_reviews.product_id = $1;
END;
$$ LANGUAGE plpgsql;

-- Create a view for product ratings summary
CREATE OR REPLACE VIEW product_ratings_summary AS
SELECT 
  product_id,
  COALESCE(AVG(rating)::NUMERIC, 0) AS average_rating,
  COUNT(*) AS review_count
FROM product_reviews
GROUP BY product_id;
